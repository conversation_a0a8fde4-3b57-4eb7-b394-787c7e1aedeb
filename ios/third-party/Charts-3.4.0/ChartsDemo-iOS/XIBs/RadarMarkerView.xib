<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13173"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="HelveticaNeueLights.ttc">
            <string>HelveticaNeue-Light</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view opaque="NO" contentMode="scaleToFill" id="iN0-l3-epB" customClass="RadarMarkerView" customModule="ChartsDemo" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="66" height="50"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" image="radar_marker.png" translatesAutoresizingMaskIntoConstraints="NO" id="L3P-FF-1zl">
                    <rect key="frame" x="0.0" y="0.0" width="66" height="50"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                </imageView>
                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="val" textAlignment="center" lineBreakMode="wordWrap" numberOfLines="0" baselineAdjustment="alignBaselines" minimumFontSize="7" translatesAutoresizingMaskIntoConstraints="NO" id="uTq-cO-p25">
                    <rect key="frame" x="25.5" y="5.5" width="15.5" height="15.5"/>
                    <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="13"/>
                    <color key="textColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <nil key="highlightedColor"/>
                </label>
            </subviews>
            <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="uTq-cO-p25" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="F41-Gk-zKe"/>
                <constraint firstItem="uTq-cO-p25" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="iN0-l3-epB" secondAttribute="leading" constant="5" id="FZu-rI-Or4"/>
                <constraint firstItem="uTq-cO-p25" firstAttribute="centerY" secondItem="iN0-l3-epB" secondAttribute="centerY" constant="-12" id="Rh3-DF-vfs"/>
                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="uTq-cO-p25" secondAttribute="trailing" constant="5" id="wZC-Tb-Ok2"/>
            </constraints>
            <nil key="simulatedStatusBarMetrics"/>
            <nil key="simulatedTopBarMetrics"/>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="label" destination="uTq-cO-p25" id="orw-8E-ANT"/>
            </connections>
            <point key="canvasLocation" x="-292" y="8"/>
        </view>
    </objects>
    <resources>
        <image name="radar_marker.png" width="133" height="100"/>
    </resources>
</document>
