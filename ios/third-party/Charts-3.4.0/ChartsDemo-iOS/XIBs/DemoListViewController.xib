<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13173"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="DemoListViewController" customModule="ChartsDemo" customModuleProvider="target">
            <connections>
                <outlet property="tableView" destination="QII-it-eiw" id="pci-Ed-uwl"/>
                <outlet property="view" destination="iN0-l3-epB" id="cMs-L7-Quz"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="44" sectionHeaderHeight="22" sectionFooterHeight="22" translatesAutoresizingMaskIntoConstraints="NO" id="QII-it-eiw">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                    <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    <connections>
                        <outlet property="dataSource" destination="-1" id="asD-89-QES"/>
                        <outlet property="delegate" destination="-1" id="q8W-Je-00Z"/>
                    </connections>
                </tableView>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="e7W-vt-N3v" firstAttribute="bottom" secondItem="QII-it-eiw" secondAttribute="bottom" id="43W-wt-Yur"/>
                <constraint firstItem="QII-it-eiw" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="6JX-cm-T75"/>
                <constraint firstAttribute="trailing" secondItem="QII-it-eiw" secondAttribute="trailing" id="Sws-jY-WYw"/>
                <constraint firstItem="QII-it-eiw" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="uo3-Uc-8Tl"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="e7W-vt-N3v"/>
        </view>
    </objects>
</document>
