<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="13196" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="13173"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="HelveticaNeueLights.ttc">
            <string>HelveticaNeue-Light</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="PiePolylineChartViewController" customModule="ChartsDemo" customModuleProvider="target">
            <connections>
                <outlet property="chartView" destination="anE-ka-ll7" id="BRq-3t-UUC"/>
                <outlet property="sliderTextX" destination="lre-9A-Aye" id="4hp-s0-eLS"/>
                <outlet property="sliderTextY" destination="n9H-fM-jsG" id="fmW-Bk-rsP"/>
                <outlet property="sliderX" destination="ekR-nK-ppT" id="lVn-aU-IGK"/>
                <outlet property="sliderY" destination="2id-KM-MqZ" id="N2P-Rc-qng"/>
                <outlet property="view" destination="30c-YJ-tW9" id="t6X-9C-FoQ"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="30c-YJ-tW9">
            <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Acu-Hm-ONQ">
                    <rect key="frame" x="289" y="4" width="78" height="35"/>
                    <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="17"/>
                    <inset key="contentEdgeInsets" minX="10" minY="7" maxX="10" maxY="7"/>
                    <state key="normal" title="Options">
                        <color key="titleColor" red="0.24040704965591431" green="0.48385584354400635" blue="0.68625134229660034" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                    </state>
                    <connections>
                        <action selector="optionsButtonTapped:" destination="-1" eventType="touchUpInside" id="NI7-mV-kT4"/>
                    </connections>
                </button>
                <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="1" minValue="1" maxValue="25" translatesAutoresizingMaskIntoConstraints="NO" id="ekR-nK-ppT">
                    <rect key="frame" x="6" y="573" width="285" height="31"/>
                    <connections>
                        <action selector="slidersValueChanged:" destination="-1" eventType="valueChanged" id="cIX-Vt-DyR"/>
                    </connections>
                </slider>
                <slider opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" value="1" minValue="1" maxValue="200" translatesAutoresizingMaskIntoConstraints="NO" id="2id-KM-MqZ">
                    <rect key="frame" x="6" y="611" width="285" height="31"/>
                    <connections>
                        <action selector="slidersValueChanged:" destination="-1" eventType="valueChanged" id="hZJ-5h-ugC"/>
                    </connections>
                </slider>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="500" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="n9H-fM-jsG">
                    <rect key="frame" x="297" y="611.5" width="70" height="30"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="70" id="JgC-Vw-8li"/>
                        <constraint firstAttribute="height" constant="30" id="qzd-1L-bEO"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="15"/>
                    <textInputTraits key="textInputTraits"/>
                </textField>
                <textField opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="500" textAlignment="center" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="lre-9A-Aye">
                    <rect key="frame" x="297" y="573.5" width="70" height="30"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="Ybz-Xr-teI"/>
                        <constraint firstAttribute="width" constant="70" id="pvO-Pm-Nqo"/>
                    </constraints>
                    <fontDescription key="fontDescription" name="HelveticaNeue-Light" family="Helvetica Neue" pointSize="15"/>
                    <textInputTraits key="textInputTraits"/>
                </textField>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="anE-ka-ll7" customClass="PieChartView" customModule="Charts">
                    <rect key="frame" x="0.0" y="47" width="375" height="501"/>
                    <color key="backgroundColor" red="0.0" green="0.0" blue="0.0" alpha="0.0" colorSpace="custom" customColorSpace="sRGB"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="25" id="XuT-Ui-Ria"/>
                    </constraints>
                    <variation key="default">
                        <mask key="constraints">
                            <exclude reference="XuT-Ui-Ria"/>
                        </mask>
                    </variation>
                </view>
            </subviews>
            <color key="backgroundColor" red="0.94117647059999998" green="0.94117647059999998" blue="0.94117647059999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstAttribute="bottom" secondItem="2id-KM-MqZ" secondAttribute="bottom" constant="26" id="0AC-oM-f39"/>
                <constraint firstItem="ekR-nK-ppT" firstAttribute="leading" secondItem="Fd3-fA-e03" secondAttribute="leading" constant="8" id="0Fb-4W-iQd"/>
                <constraint firstItem="n9H-fM-jsG" firstAttribute="leading" secondItem="2id-KM-MqZ" secondAttribute="trailing" constant="8" id="0Yy-6X-Pdi"/>
                <constraint firstItem="ekR-nK-ppT" firstAttribute="centerY" secondItem="lre-9A-Aye" secondAttribute="centerY" id="3D7-U1-VQM"/>
                <constraint firstItem="Fd3-fA-e03" firstAttribute="trailing" secondItem="Acu-Hm-ONQ" secondAttribute="trailing" constant="8" id="IIg-R0-B3N"/>
                <constraint firstItem="Fd3-fA-e03" firstAttribute="trailing" secondItem="lre-9A-Aye" secondAttribute="trailing" constant="8" id="Kd4-V3-hub"/>
                <constraint firstItem="n9H-fM-jsG" firstAttribute="centerY" secondItem="2id-KM-MqZ" secondAttribute="centerY" id="O0I-Eo-Qq5"/>
                <constraint firstItem="anE-ka-ll7" firstAttribute="leading" secondItem="Fd3-fA-e03" secondAttribute="leading" id="RiV-Bw-HVx"/>
                <constraint firstItem="2id-KM-MqZ" firstAttribute="top" secondItem="ekR-nK-ppT" secondAttribute="bottom" constant="8" id="TCF-2x-BF2"/>
                <constraint firstItem="ekR-nK-ppT" firstAttribute="top" secondItem="anE-ka-ll7" secondAttribute="bottom" constant="25" id="VRh-6e-hvp"/>
                <constraint firstItem="anE-ka-ll7" firstAttribute="top" secondItem="30c-YJ-tW9" secondAttribute="top" constant="47" id="cq6-Wq-JBT"/>
                <constraint firstItem="ekR-nK-ppT" firstAttribute="top" secondItem="anE-ka-ll7" secondAttribute="bottom" constant="100" id="lym-Gz-c9p"/>
                <constraint firstItem="Fd3-fA-e03" firstAttribute="trailing" secondItem="anE-ka-ll7" secondAttribute="trailing" id="n8e-xI-OiN"/>
                <constraint firstItem="lre-9A-Aye" firstAttribute="leading" secondItem="ekR-nK-ppT" secondAttribute="trailing" constant="8" id="pCP-XY-Sh8"/>
                <constraint firstItem="Fd3-fA-e03" firstAttribute="trailing" secondItem="n9H-fM-jsG" secondAttribute="trailing" constant="8" id="rzb-88-LAf"/>
                <constraint firstItem="2id-KM-MqZ" firstAttribute="leading" secondItem="Fd3-fA-e03" secondAttribute="leading" constant="8" id="sl2-pO-S2f"/>
                <constraint firstItem="Acu-Hm-ONQ" firstAttribute="top" secondItem="30c-YJ-tW9" secondAttribute="top" constant="4" id="wBl-J9-CLZ"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="Fd3-fA-e03"/>
            <variation key="default">
                <mask key="constraints">
                    <exclude reference="lym-Gz-c9p"/>
                </mask>
            </variation>
            <point key="canvasLocation" x="157.5" y="222.5"/>
        </view>
    </objects>
</document>
