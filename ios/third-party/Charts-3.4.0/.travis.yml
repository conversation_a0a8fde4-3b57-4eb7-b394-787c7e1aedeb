language: objective-c
osx_image: xcode11
matrix:
  include:
    - osx_image: xcode11
      env: PLATFORM="iOS"
    - osx_image: xcode11
      env: PLATFORM="tvOS"
    - osx_image: xcode11
      env: PLATFORM="macOS"
env:
  global:
    - LANG=en_US.UTF-8
    - secure: HRl5Cg3/LmTJYCSDGtV8Fiqf6OgWXR6C5YkNviOElU9YmcCTvnmE7MCm+gK8hJNIQ2mWCBTlxTC7jJxeYlx0D+JQ6IhRc75B4zXgbtth6HCzlvhWDcBbNU/K+JPDjy2EAoVgvf8Xseq6GqM4WcVg2Pwr9PS3aaH/SKDE6yVoPXRM2wXRAxIdxayqm/RQgG88jAKgg+Ub5DKW/c9NLLbUf/WdY9SgQDJtqeZCMv7G3/IzW34KZgVPsqSrPByzZXbf7KgYkYLQlscH4mcUctk1qxhdCae//zcgUZo/utpXWbPXQqtvDpuqrg9IRvoGxa/D1I7dLSJsVnk4aP7RW18vn4HJ82C2AiEp+lU2TQE9y5lXcGssZVIa5aZNTWL90HVer3Kg6AnHffvdCSZ/UHsMZJFURUXL9fRIlWVGuEDeQOqlV6lUmEtp/BiEdV4D5PuCyT6snsmEwkc51goyqh6uS6XhJyqYEQdxdoybznwp91tVmASsRybppIGHTkcWN5csF4zQt40tEwd8LoqM+bBUo0wxKiKsNsecL4JA+YDYs2saZY9U3s8jAaIon+qDu071fPRaPEvBhYHYDhy4cTvBXibyGUYGHkrgLu0w19wDfHcJKt2In58yOmaCCLNoTGp/ZLNVRyZ8VDlj/NdJ2dWpMsL2Aj7OxU065kCL2FlvuRU=
cache:
  - bundler
before_install:
  - brew update
  - brew upgrade carthage || true
  - carthage version
  - carthage bootstrap --platform $PLATFORM
script:
  - bundle exec rake ci[$PLATFORM]
after_success:
  - bash <(curl -s https://codecov.io/bash)
