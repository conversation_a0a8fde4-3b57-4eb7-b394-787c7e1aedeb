//
//  AudioPlayer.m
//  Runner
//
//  Created by 何飞 on 2020/5/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import <AVFoundation/AVFoundation.h>

#import "AudioPlayer.h"
#import "Log.h"


@interface AudioPlayer()<AVAudioPlayerDelegate>
@property (strong, nonatomic) AVAudioPlayer* player;
@end


@implementation AudioPlayer{
    NSTimer* _timer;
    NSTimeInterval _lastNotifyPlayback;
}

-(void) play {
    [self setAudioSessionDefault];
    if (!self.player) {
        NSError* error = nil;
        self.player = [[AVAudioPlayer alloc] initWithContentsOfURL:[NSURL fileURLWithPath:self.src] error:&error];
        self.player.numberOfLoops = 0;
        self.player.delegate = self;

        if (error != nil) {
            dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
                if (self.delegate) {
                    [self.delegate audioPlayer:self error:error];
                }
            });
            return;
        }
    }
    [self.player play];
    [self startTimer];
}

-(void) pause {
    [self.player pause];
    [self stopTimer];
}

-(void) stop {
    [self.player stop];
    
    [self stopTimer];
}

- (NSTimeInterval)currentPlaybackTime {
    return self.player.currentTime;
}



-(BOOL)startTimer
{
    if (self.emitTimeupdateRate<=0) return NO;
    
    if(!_timer)
    {
        _timer = [NSTimer scheduledTimerWithTimeInterval:self.emitTimeupdateRate / 1000.0 target:self selector:@selector(timerProc) userInfo:nil repeats:YES];
        //http://stackoverflow.com/questions/1679814/iphone-phone-goes-to-sleep-even-if-idletimerdisabled-is-yes/11077931
//        [UIApplication sharedApplication].idleTimerDisabled = NO;
//        [UIApplication sharedApplication].idleTimerDisabled = YES;
        return YES;
    }
    return NO;
}

-(void)timerProc
{
    NSTimeInterval current =  _player.currentTime;
    if (current != _lastNotifyPlayback) {
        if (self.delegate) {
            [self.delegate audioPlayer:self onTimeupdate:current];
        }
        
        _lastNotifyPlayback = current;
    }
}

- (void)stopTimer
{
    if(_timer)
    {
        [_timer invalidate];
        _timer = nil;
//        [UIApplication sharedApplication].idleTimerDisabled = NO;
    }
}

- (void)setAudioSessionDefault
{
    AVAudioSession *audioSession = [AVAudioSession sharedInstance];

    //默认情况下扬声器播放
    [audioSession setCategory:AVAudioSessionCategoryPlayback withOptions:AVAudioSessionCategoryOptionMixWithOthers | AVAudioSessionCategoryOptionDefaultToSpeaker error:nil];
    [audioSession setActive:YES error:nil];
}

#pragma mark AVAudioPlayerDelegate
- (void)audioPlayerDidFinishPlaying:(AVAudioPlayer *)player successfully:(BOOL)flag {
    Log(@"AudioPlayer.audioPlayerDidFinishPlaying flag = %d", flag);
    if (self.delegate) {
        [self.delegate audioPlayerOnEnd:self];
    }
}

- (void)audioPlayerDecodeErrorDidOccur:(AVAudioPlayer *)player error:(NSError * __nullable)error {
    Log(@"AudioPlayer.audioPlayerDidFinishPlaying error = %@", error);
    if (self.delegate) {
        [self.delegate audioPlayer:self error:error];
    }
}

@end
