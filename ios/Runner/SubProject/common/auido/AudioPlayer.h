//
//  AudioPlayer.h
//  Runner
//
//  Created by 何飞 on 2020/5/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class AudioPlayer;

@protocol AudioPlayerDelegate <NSObject>

//播放出错
-(void) audioPlayer:(AudioPlayer*) player error:(NSError*) error;

-(void) audioPlayerOnEnd:(AudioPlayer*) player;

-(void) audioPlayer:(AudioPlayer*) player onTimeupdate:(NSTimeInterval) currentPlaybackTime;

@end


@interface AudioPlayer : NSObject
@property (weak, nonatomic) id<AudioPlayerDelegate> delegate;
@property (assign, nonatomic) NSInteger emitTimeupdateRate;
@property (strong, nonatomic) NSString* src;
@property (nonatomic,readonly) NSTimeInterval currentPlaybackTime;

-(void) play;

-(void) pause;

-(void) stop;

@end

NS_ASSUME_NONNULL_END
