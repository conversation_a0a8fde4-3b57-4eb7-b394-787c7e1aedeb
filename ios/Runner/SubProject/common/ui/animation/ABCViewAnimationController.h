#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "ABCAnimationDefine.h"


@protocol ABCViewAnimationControllerDelegate
- (void) onTransitionFinish:(UIView*)view;
@end

@interface ABCViewAnimationController :NSObject
@property(nonatomic,weak,readonly) UIScreenEdgePanGestureRecognizer *dismissalGestureRecognizer;
@property(nonatomic,assign) ABCViewControllerAnimationType animationType;
@property(nonatomic,assign) ABCViewPresentionAnimationStyle animationStyle;


@property(nonatomic,weak) id<ABCViewAnimationControllerDelegate> delegate;
@property(nonatomic,assign) BOOL animationEnable;


//- (void)configueGestureRecognizerForView:(UIView *)view toView:(UIView*) toView;

@end


@interface UIView(ABCEdgeGesture)

- (void)setPresentionStyle:(ABCViewPresentionAnimationStyle)style toView:(UIView*)toView delegate:(id<ABCViewAnimationControllerDelegate>) delegate;

- (void) removeAnimation;

- (void) setEnablePresentionStyle:(BOOL)enable;

@end
