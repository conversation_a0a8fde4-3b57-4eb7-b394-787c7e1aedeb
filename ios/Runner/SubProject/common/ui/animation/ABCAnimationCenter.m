#import "ABCAnimationCenter.h"
#import "objc/runtime.h"

static ABCAnimationCenter *_animationCenter = nil;

@implementation ABCAnimationCenter

+ (ABCAnimationCenter *)defaultCenter
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        _animationCenter = [[ABCAnimationCenter alloc] init];
    });
    
    return _animationCenter;
}


#pragma mark -
#pragma mark 渐隐渐显动画
+ (CATransition *) ABCFadeAnimationWithDuration:(CGFloat)duration
{
    CATransition *animation = [CATransition animation];
    animation.type = kCATransitionFade;
    animation.duration = duration;
    animation.timingFunction = [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseOut];
    return animation;
}

+ (CATransition *) ABCFadeAnimation
{
    return [ABCAnimationCenter ABCFadeAnimationWithDuration:ABCFadeAnimationDuration];
}


- (id)init
{
    self = [super init];
    if (self) {

        _curve = CurveTypeOpera;
        _easing = ABCAnimationEaseOut;
        
        [self setDefaultFunctionWithCurve:_curve easing:_easing];

    }
    return self;
}

- (void)setDefaultFunction:(AHEasingFunction)easingFunction
{
    _function = easingFunction;
}

- (AHEasingFunction)defaultFunction
{
    return _function;
}

- (NSUInteger)defaultEasing
{
    return _easing;
}

- (NSUInteger)defaultCurve
{
    return _curve;
}

- (void)setDefaultFunctionWithCurve:(NSUInteger)curve easing:(ABCAnimationEasingMode)easing
{
    _curve = curve;
    _easing = easing;
        
    [self setDefaultFunction:[ABCAnimationCenter functionWithCurve:curve easing:easing]];
}

+ (AHEasingFunction)functionWithCurve:(ABCAnimationCurveType)curve easing:(ABCAnimationEasingMode)easing
{
    AHEasingFunction currentFunction = nil;
    switch(curve)
	{
		case CurveTypeLinear:
			currentFunction = LinearInterpolation;
			break;
		case CurveTypeQuadratic:
			currentFunction = (easing == EaseIn) ? QuadraticEaseIn : (easing == EaseOut) ? QuadraticEaseOut : QuadraticEaseInOut;
			break;
		case CurveTypeCubic:
			currentFunction = (easing == EaseIn) ? CubicEaseIn : (easing == EaseOut) ? CubicEaseOut : CubicEaseInOut;
			break;
		case CurveTypeQuartic:
			currentFunction = (easing == EaseIn) ? QuarticEaseIn : (easing == EaseOut) ? QuarticEaseOut : QuarticEaseInOut;
			break;
		case CurveTypeQuintic:
			currentFunction = (easing == EaseIn) ? QuinticEaseIn : (easing == EaseOut) ? QuinticEaseOut : QuinticEaseInOut;
			break;
		case CurveTypeSine:
			currentFunction = (easing == EaseIn) ? SineEaseIn : (easing == EaseOut) ? SineEaseOut : SineEaseInOut;
			break;
		case CurveTypeCircular:
			currentFunction = (easing == EaseIn) ? CircularEaseIn : (easing == EaseOut) ? CircularEaseOut : CircularEaseInOut;
			break;
		case CurveTypeExpo:
			currentFunction = (easing == EaseIn) ? ExponentialEaseIn : (easing == EaseOut) ? ExponentialEaseOut : ExponentialEaseInOut;
			break;
		case CurveTypeElastic:
			currentFunction = (easing == EaseIn) ? ElasticEaseIn : (easing == EaseOut) ? ElasticEaseOut : ElasticEaseInOut;
			break;
		case CurveTypeBack:
			currentFunction = (easing == EaseIn) ? BackEaseIn : (easing == EaseOut) ? BackEaseOut : BackEaseInOut;
			break;
		case CurveTypeOpera:
			currentFunction = NewCurve;
			break;
	}

    return currentFunction;
}

- (AHEasingFunction)defaultFunctionWithEasing:(ABCAnimationEasingMode)easing
{
    return [ABCAnimationCenter functionWithCurve:_curve easing:easing];
}

@end


@implementation UINavigationController (ABCAnimationForIOS7)

static void * AnimationExtensionKey = (void *)@"AnimationControllerKey";
static void * InteractiveExtensionrKey = (void *)@"InteractiveControllerKey";

- (void)setAnimationExtension:(ABCBaseAnimation *)animationExtension
{
    objc_setAssociatedObject(self, AnimationExtensionKey, animationExtension, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (ABCBaseAnimation*)animationExtension
{
    return objc_getAssociatedObject(self, AnimationExtensionKey);
}

- (void)setInteractionExtensioin:(ABCBaseInteraction *)interactionExtensioin
{
    objc_setAssociatedObject(self, InteractiveExtensionrKey, interactionExtensioin, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

- (ABCBaseInteraction*)interactionExtensioin
{
    return objc_getAssociatedObject(self, InteractiveExtensionrKey);
}


- (id<UIViewControllerAnimatedTransitioning>)navigationController:(UINavigationController *)navigationController animationControllerForOperation:(UINavigationControllerOperation)operation fromViewController:(UIViewController *)fromVC toViewController:(UIViewController *)toVC {
    
    if (self.interactionExtensioin) {
        [self.interactionExtensioin wireToViewController:toVC forOperation:ABCInteractionOperationPop];
    }
    
    if (self.animationExtension) {
        self.animationExtension.reverse = operation == UINavigationControllerOperationPop;
    }
    
    return self.animationExtension;
}

- (id <UIViewControllerInteractiveTransitioning>)navigationController:(UINavigationController *)navigationController interactionControllerForAnimationController:(id <UIViewControllerAnimatedTransitioning>) animationController {
    
    return self.interactionExtensioin && self.interactionExtensioin.interactionInProgress ? self.interactionExtensioin : nil;
}
@end

