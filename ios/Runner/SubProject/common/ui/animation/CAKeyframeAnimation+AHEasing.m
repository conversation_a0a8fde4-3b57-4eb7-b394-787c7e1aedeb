#import "CAKeyframeAnimation+AHEasing.h"
#import <UIKit/UIKit.h>

#if !defined(AHEasingDefaultKeyframeCount)

// The larger this number, the smoother the animation
#define AHEasingDefaultKeyframeCount 60

#endif

@implementation CAKeyframeAnimation (AHEasing)

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromValue:(CGFloat)fromValue toValue:(CGFloat)toValue keyframeCount:(size_t)keyframeCount
{
	NSMutableArray *values = [NSMutableArray arrayWithCapacity:keyframeCount];
	
	CGFloat t = 0.0;
	CGFloat dt = 1.0 / (keyframeCount - 1);
	for(size_t frame = 0; frame < keyframeCount; ++frame, t += dt)
	{
		CGFloat value = fromValue + function(t) * (toValue - fromValue);
		[values addObject:[NSNumber numberWithFloat:value]];
	}
	
	CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:path];
	[animation setValues:values];
	return animation;
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromValue:(CGFloat)fromValue toValue:(CGFloat)toValue
{
    return [self animationWithKeyPath:path function:function fromValue:fromValue toValue:toValue keyframeCount:AHEasingDefaultKeyframeCount];
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromPoint:(CGPoint)fromPoint toPoint:(CGPoint)toPoint keyframeCount:(size_t)keyframeCount
{
	NSMutableArray *values = [NSMutableArray arrayWithCapacity:keyframeCount];
	
	CGFloat t = 0.0;
	CGFloat dt = 1.0 / (keyframeCount - 1);
	for(size_t frame = 0; frame < keyframeCount; ++frame, t += dt)
	{
		CGFloat x = fromPoint.x + function(t) * (toPoint.x - fromPoint.x);
		CGFloat y = fromPoint.y + function(t) * (toPoint.y - fromPoint.y);
		[values addObject:[NSValue valueWithCGPoint:CGPointMake(x, y)]];
	}
	
	CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:path];
	[animation setValues:values];
	return animation;
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromPoint:(CGPoint)fromPoint toPoint:(CGPoint)toPoint
{
    return [self animationWithKeyPath:path function:function fromPoint:fromPoint toPoint:toPoint keyframeCount:AHEasingDefaultKeyframeCount];
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromSize:(CGSize)fromSize toSize:(CGSize)toSize keyframeCount:(size_t)keyframeCount
{
	NSMutableArray *values = [NSMutableArray arrayWithCapacity:keyframeCount];
	
	CGFloat t = 0.0;
	CGFloat dt = 1.0 / (keyframeCount - 1);
	for(size_t frame = 0; frame < keyframeCount; ++frame, t += dt)
	{
		CGFloat w = fromSize.width + function(t) * (toSize.width - fromSize.width);
		CGFloat h = fromSize.height + function(t) * (toSize.height - fromSize.height);
		[values addObject:[NSValue valueWithCGSize:CGSizeMake(w, h)]];
	}
	
	CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:path];
	[animation setValues:values];
	return animation;
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromSize:(CGSize)fromSize toSize:(CGSize)toSize
{
    return [self animationWithKeyPath:path function:function fromSize:fromSize toSize:toSize keyframeCount:AHEasingDefaultKeyframeCount];
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromRect:(CGRect)fromRect toRect:(CGRect)toRect keyframeCount:(size_t)keyframeCount
{
    
    NSMutableArray *values = [NSMutableArray arrayWithCapacity:keyframeCount];
	
	CGFloat t = 0.0;
	CGFloat dt = 1.0 / (keyframeCount - 1);
	for(size_t frame = 0; frame < keyframeCount; ++frame, t += dt)
	{
        CGFloat x = fromRect.origin.x + function(t) * (toRect.origin.x - fromRect.origin.x);
		CGFloat y = fromRect.origin.y + function(t) * (toRect.origin.y - fromRect.origin.y);
		CGFloat w = fromRect.size.width + function(t) * (toRect.size.width - fromRect.size.width);
		CGFloat h = fromRect.size.height + function(t) * (toRect.size.height - fromRect.size.height);
		[values addObject:[NSValue valueWithCGRect:CGRectMake(x, y, w, h)]];
	}
	
	CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:path];
	[animation setValues:values];
	return animation;
}

+ (id)animationWithKeyPath:(NSString *)path function:(AHEasingFunction)function fromRect:(CGRect)fromRect toRect:(CGRect)toRect
{
    return [self animationWithKeyPath:path function:function fromRect:fromRect toRect:toRect keyframeCount:AHEasingDefaultKeyframeCount];
}

+ (id)animationWithKeyPath:(NSString *)path
                  function:(AHEasingFunction)function
             fromTransform:(CGAffineTransform)fromTransform
               toTransform:(CGAffineTransform)toTransform
             keyframeCount:(size_t)keyframeCount
{
    NSMutableArray *values = [NSMutableArray arrayWithCapacity:keyframeCount];
	
	CGFloat t = 0.0;
	CGFloat dt = 1.0 / (keyframeCount - 1);
	for(size_t frame = 0; frame < keyframeCount; ++frame, t += dt)
	{
        CGFloat a = fromTransform.a + function(t) * (toTransform.a - fromTransform.a);
        CGFloat b = fromTransform.b + function(t) * (toTransform.b - fromTransform.b);
        CGFloat c = fromTransform.c + function(t) * (toTransform.c - fromTransform.c);
        CGFloat d = fromTransform.d + function(t) * (toTransform.d - fromTransform.d);
        CGFloat tx = fromTransform.tx + function(t) * (toTransform.tx - fromTransform.tx);
        CGFloat ty = fromTransform.ty + function(t) * (toTransform.ty - fromTransform.ty);
        CGAffineTransform transform = CGAffineTransformMake(a, b, c, d, tx, ty);
        [values addObject:[NSValue valueWithCATransform3D:CATransform3DMakeAffineTransform(transform)]];
    }
    CAKeyframeAnimation *animation = [CAKeyframeAnimation animationWithKeyPath:path];
	[animation setValues:values];
	return animation;
}

+ (id)animationWithKeyPath:(NSString *)path
                  function:(AHEasingFunction)function
             fromTransform:(CGAffineTransform)fromTransform
               toTransform:(CGAffineTransform)toTransform
{
    return [self animationWithKeyPath:path
                             function:function
                        fromTransform:fromTransform
                          toTransform:toTransform
                        keyframeCount:AHEasingDefaultKeyframeCount];
}

@end
