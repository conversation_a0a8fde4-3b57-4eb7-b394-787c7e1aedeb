#ifndef ABCAnimationDefine_h
#define ABCAnimationDefine_h

#define CUSTOM_ANIMATION_DURING 0.4
#define CUSTOM_ANIMATION_DURING_FAST  0.3
#define CUSTOM_ANIMATION_DURING_SLOW  0.5
#define CUSTOM_ANIMATION_MASKPATH_KEY  @"CUSTOM_ANIMATION_MASKPATH_KEY"
#define CUSTOM_ANIMATION_MASK_ORIGIN_SIZE_KEY @"CUSTOM_ANIMATION_MASK_ORIGIN_SIZE_KEY"

//easeOutCubic
//#define ABC_DEFAULT_TIMING_FUNCTION [CAMediaTimingFunction functionWithControlPoints:0.215:0.61:0.355:1]
#define ABC_DEFAULT_TIMING_FUNCTION [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionEaseInEaseOut]
#define ABC_DIALOG_DISMISS_TIMING_FUNCTION [CAMediaTimingFunction functionWithControlPoints:.25 :.46 :.45 :.94]
#define ABC_MOVING_TIMING_FUNCTION [CAMediaTimingFunction functionWithControlPoints:.5 :0 :.5 :0]

//6.0动画曲线
#define ABC_DEFAULT_ANIMATION_TIME .3
#define ABC_DEFAULT_MASK_DURATION .3
#define ABC_SETTING_CELL_TRANSITION_DURATION .3
#define ABC_DISMISS_MENU_ANIMATION_TIME .3
#define ABC_DEFAULT_ANIMATION_ALPHA .6
#define ABC_DEFAULT_ANIMATION_SCALE .8

typedef NS_ENUM(NSUInteger, ABCViewControllerAnimationType) {
    ABCViewControllerAnimationTypePresent,
    ABCViewControllerAnimationTypeDismiss,
};

typedef NS_ENUM(NSUInteger, ABCViewControllerPresentionAnimationStyle) {
    ABCViewControllerPresentionAnimationNone,
    ABCViewControllerPresentionAnimationFlipFromRight,
    ABCViewControllerPresentionAnimationFromDown,
};


typedef NS_ENUM(NSUInteger, ABCViewPresentionAnimationStyle) {
    ABCViewPresentionAnimationNone,
    ABCViewPresentionAnimationFlipFromRight,
    ABCViewPresentionAnimationFromDown,
};

typedef NS_ENUM(NSUInteger, ABCPresentType) {
    PRESENT_IN_NAVIGATION,
    PRESENT_ONLY
};

#ifdef AH_EASING_USE_DBL_PRECIS
#define AHFloat double
#else
#define AHFloat float
#endif

typedef AHFloat (*AHEasingFunction)(AHFloat);
#endif /* ABCAnimationDefine_h */
