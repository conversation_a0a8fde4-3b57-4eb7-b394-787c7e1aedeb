#import "ABCViewAnimationController.h"
#import "UIView+ABCAnimation.h"
#import "ABCBaseViewController.h"
#import "ABCMacros.h"

#import <objc/runtime.h>


//@interface ABCViewAnimationController () <UIGestureRecognizerDelegate>

@interface ABCViewAnimationController ()

@property(nonatomic) BOOL interactive;

@end

@implementation ABCViewAnimationController
{
    UIView __weak *_view;
    UIViewController __weak *_presentingViewController;



    CGRect _dismissViewOldFrame;

    UIView __weak *_dismissFromView;
    UIView __weak *_dismissToView;

    CGFloat _currentScale;

    UIImageView *_backgroundView;
}
- (instancetype)init
{
    self = [super init];
    if (self) {
        _animationEnable = YES;
    }

    return self;
}

- (void)dealloc
{

}

- (UIPresentationController *)presentationControllerForPresentedViewController:(UIViewController *)presented presentingViewController:(UIViewController *)presenting sourceViewController:(UIViewController *)source NS_AVAILABLE_IOS(8_0);
{
    return nil;
}

- (NSTimeInterval)transitionDuration:(id <UIViewControllerContextTransitioning>)transitionContext
{
    return ABC_DEFAULT_ANIMATION_TIME;
}

- (void)animateTransition:(id <UIViewControllerContextTransitioning>)transitionContext
{
    if (self.interactive || !self.animationEnable)//扣边的动画在手势中做
    {
        return;
    }

    UIView *containerView = [transitionContext containerView];

    UIViewController *fromViewController = [transitionContext viewControllerForKey:UITransitionContextFromViewControllerKey];
    UIViewController *toViewController = [transitionContext viewControllerForKey:UITransitionContextToViewControllerKey];

    UIView *fromView = fromViewController.view;
    UIView *toView = toViewController.view;

    CGRect finalFrame = [transitionContext finalFrameForViewController:toViewController];

    if (self.animationType == ABCViewControllerAnimationTypePresent) {

        CGRect frame = finalFrame;
        if(self.animationStyle == ABCViewPresentionAnimationFlipFromRight) {
            frame.origin.x += containerView.bounds.size.width;
        } else {
            frame.origin.y += containerView.bounds.size.height;
        }
        toView.frame = frame;
        [containerView addSubview:toView];

        //Present的动画
        containerView.backgroundColor = [UIColor blackColor];

        [toView animateToFrameForShowDefault:finalFrame duration:[self transitionDuration:transitionContext] completionBlock:NULL];

#ifdef ABC_USE_SCALE_ANIMATION_ON_VIEW_TRANSITION
        DefineWeakVarBeforeBlock(fromView);
        [fromView animateFromScale:1.0 toScale:ABC_DEFAULT_ANIMATION_SCALE duration:[self transitionDuration:transitionContext] completionBlock:^{
            DefineStrongVarInBlock(fromView);
            [fromView removeScaleAnimation];
        }];
#endif
        DefineWeakVarBeforeBlock(fromView);
        DefineWeakVarBeforeBlock(transitionContext);
        [fromView animateToOpacityDefault:ABC_DEFAULT_ANIMATION_ALPHA duration:[self transitionDuration:transitionContext] completionBlock:^{
            DefineStrongVarInBlock(transitionContext);
            DefineStrongVarInBlock(fromView);
            fromView.alpha = 1;
            if (![transitionContext isInteractive]) {
                [transitionContext completeTransition:YES];
            }
        }];
    }
    else//dismiss
    {
        // 防止8.0系统在In-call statusbar下界面异常的问题 by zuckchen
        if (NSFoundationVersionNumber > NSFoundationVersionNumber_iOS_7_1) {
            if (!CGRectEqualToRect(finalFrame, toView.frame)) {
                toView.frame = finalFrame;
            }
        }

        [containerView addSubview:toView];
        [containerView addSubview:fromView];

        containerView.backgroundColor = [UIColor blackColor];


        CGRect frame = fromView.frame;
        frame.origin.x += containerView.bounds.size.width;
        [fromView animateToFrameForHideDefault:frame duration:[self transitionDuration:transitionContext] function:self.interactive ? [CAMediaTimingFunction functionWithName:kCAMediaTimingFunctionLinear] : ABC_DIALOG_DISMISS_TIMING_FUNCTION completionBlock:NULL];


        DefineWeakVarBeforeBlock(toView);

#ifdef ABC_USE_SCALE_ANIMATION_ON_VIEW_TRANSITION
        [toView animateFromScale:ABC_DEFAULT_ANIMATION_SCALE toScale:1.0 duration:[self transitionDuration:transitionContext] completionBlock:^{
            DefineStrongVarInBlock(toView);
            [toView removeScaleAnimation];
            toView.transform = CGAffineTransformIdentity;
        }];
#endif

        DefineWeakVarBeforeBlock(fromView);
        DefineWeakVarBeforeBlock(transitionContext);
        toView.alpha = ABC_DEFAULT_ANIMATION_ALPHA;
        [toView animateToOpacityDefault:1.0 duration:[self transitionDuration:transitionContext] completionBlock:^{
            DefineStrongVarInBlock(toView);
            DefineStrongVarInBlock(fromView);
            DefineStrongVarInBlock(transitionContext);

            if ([transitionContext transitionWasCancelled]) {

                //workaround for bug of iOS7...蛋疼 >_<
                //fromView's tranform is incorrect after being removed from containerView
                fromView.transform = containerView.transform;
                fromView.frame = containerView.frame;
                toView.alpha = 1.0;
            }

            [transitionContext completeTransition:![transitionContext transitionWasCancelled]];
        }];
    }

}

#pragma mark -扣边动画
//- (void)configueGestureRecognizerForView:(UIView *)view toView:(UIView*) toView
//{
//    UIScreenEdgePanGestureRecognizer *gestureRecognizer = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGestureRecognizer:)];
//    gestureRecognizer.edges = UIRectEdgeLeft;
//    [view addGestureRecognizer:gestureRecognizer];
//    gestureRecognizer.delegate = self;
//    _dismissalGestureRecognizer = gestureRecognizer;
//
//    _view = view;
//    _dismissFromView = _view;
//    _dismissToView = toView;
//}
//
//- (BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer
//{
//    return self.animationEnable;
//}
//
//- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer canBePreventedByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer
//{
//    if ([gestureRecognizer isMemberOfClass:[UIPanGestureRecognizer class]]) {
//        return YES;
//    }
//    return NO;
//}
//
//- (void)handlePanGestureRecognizer:(UIScreenEdgePanGestureRecognizer *)gestureRecognizer
//{
//    CGPoint translation = [gestureRecognizer translationInView:gestureRecognizer.view];
//
//    CGPoint velocity = [gestureRecognizer velocityInView:gestureRecognizer.view];
//
//    switch (gestureRecognizer.state) {
//        case UIGestureRecognizerStateBegan:
//        {
//            self.interactive = YES;
//            _dismissViewOldFrame =_view.frame;
//            break;
//        }
//        case UIGestureRecognizerStateChanged:
//        {
//            [self _mttUpdateInteractiveTransition:translation];
//            break;
//        }
//        case UIGestureRecognizerStateEnded:
//        case UIGestureRecognizerStateCancelled:
//        {
//            if ((translation.x < _view.bounds.size.width / 2 && velocity.x < 500) || gestureRecognizer.state == UIGestureRecognizerStateCancelled)
//            {
//                // 抬手左滑
//                [self _mttCancelInteractiveTransitionWithTranslation:translation];
//            }
//            else
//            {
//                // 抬手右滑
//                [self _mttFinishInteractiveTransitionWithTranslation:translation];
//            }
//
//
//            self.interactive = NO;
//
//            break;
//        }
//        default:
//            break;
//    }
//
//}
//
//- (void)updateInteractiveTransition:(CGFloat)percentComplete
//{
//}
//
//- (void)_mttUpdateInteractiveTransition:(CGPoint) translation
//{
//    NSLog(@"_mttUpdateInteractiveTransition x = %f, y = %f", translation.x, translation.y);
//    UIView *fromView = _dismissFromView;
//    CGFloat translationX = translation.x;
//    if (translationX < 0) {
//        translationX = 0;
//    }
//    if (fromView)
//    {
//        if(self.animationStyle == ABCViewControllerPresentionAnimationFlipFromRight)//左右方向
//        {
//            fromView.frame = CGRectMake(translationX, CGRectGetMinY(_dismissViewOldFrame), CGRectGetWidth(_dismissViewOldFrame), CGRectGetHeight(_dismissViewOldFrame));
//        }
//        else
//        {
//            fromView.frame = CGRectMake(CGRectGetMinX(_dismissViewOldFrame), translation.y, CGRectGetWidth(_dismissViewOldFrame), CGRectGetHeight(_dismissViewOldFrame));
//        }
//
//        fromView.layer.shadowColor = [UIColor blackColor].CGColor;
//        fromView.layer.shadowOffset = CGSizeMake(0, 4);
//        fromView.layer.shadowRadius = 6;
//        fromView.layer.shadowOpacity = 0.4 * ((CGRectGetWidth(_dismissViewOldFrame) - translation.x) / CGRectGetWidth(_dismissViewOldFrame));
//
//        CGFloat alpha = 0.2 * ((CGRectGetWidth(_dismissViewOldFrame) - translation.x) / CGRectGetWidth(_dismissViewOldFrame));
//        //fromView.superview.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:alpha];
//        fromView.superview.layer.backgroundColor = [UIColor colorWithRed:0 green:0 blue:0 alpha:alpha].CGColor;
//    }
//
//    CGFloat fraction = translationX / fromView.bounds.size.width;
//
//    UIView *toView = _dismissToView;
//
//
//    if (toView)
//    {
//        //先关闭动画切换过程中修改alpha的功能
//        toView.alpha = ABC_DEFAULT_ANIMATION_ALPHA + fraction * (1 - ABC_DEFAULT_ANIMATION_ALPHA);
//    }
//}
//
//- (void)_mttCancelInteractiveTransitionWithTranslation:(CGPoint) translation
//{
//    DefineWeakSelfBeforeBlock();
//    UIView *fromView = _dismissFromView;
//    UIView *toView = _dismissToView;
//    if (fromView && toView)
//    {
//        CGFloat fraction = translation.x / fromView.bounds.size.width;
//        CGFloat duration = fraction * ABC_DEFAULT_ANIMATION_TIME;
//        CGFloat maskOpacity = 1.0;
//        if ((NSFoundationVersionNumber > NSFoundationVersionNumber_iOS_7_1) || UIInterfaceOrientationIsPortrait([[UIApplication sharedApplication] statusBarOrientation])) {
//            [fromView animateToFrameForShowDefault:_dismissViewOldFrame maskOpacity:maskOpacity duration:duration completionBlock:nil];
//        }
//        else {
//            [fromView animateToFrameForShowDefault:CGRectMake(_dismissViewOldFrame.origin.x, _dismissViewOldFrame.origin.y, fromView.frame.size.width, fromView.frame.size.height)
//                                       maskOpacity:maskOpacity duration:duration completionBlock:nil];
//        }
//
//#ifdef ABC_USE_SCALE_ANIMATION_ON_VIEW_TRANSITION
//        [toView animateFromScale:_currentScale toScale:ABC_DEFAULT_ANIMATION_SCALE duration:duration completionBlock:^{
//            UIView *toView = _dismissToView;
//            [toView removeScaleAnimation];
//        }];
//#endif
//
//        [toView animateToOpacityDefault:ABC_DEFAULT_ANIMATION_ALPHA duration:duration completionBlock:^{
//            if (_backgroundView) {
//                [_backgroundView removeFromSuperview];
//            }
//            DefineStrongSelfInBlock(sself);
//            [sself cancelInteractiveTransition];
//        }];
//    }
//}
//
//- (void)cancelInteractiveTransition
//{
//    if ((NSFoundationVersionNumber > NSFoundationVersionNumber_iOS_7_1) || UIInterfaceOrientationIsPortrait([[UIApplication sharedApplication] statusBarOrientation])) {
//    }
//    else {
//        UIInterfaceOrientation orientation = [[UIApplication sharedApplication] statusBarOrientation];
//        if (orientation == UIInterfaceOrientationLandscapeLeft) {
//            CGAffineTransform rotation = CGAffineTransformMakeRotation(-M_PI/2);
//            [_dismissFromView setTransform:rotation];
//        }
//        else if (orientation == UIInterfaceOrientationLandscapeRight) {
//            CGAffineTransform rotation = CGAffineTransformMakeRotation(M_PI/2);
//            [_dismissFromView setTransform:rotation];
//        }
//
//        _dismissFromView.frame = CGRectMake(0, CGRectGetMinY(_dismissViewOldFrame),_dismissFromView.frame.size.width, _dismissFromView.frame.size.height);
//    }
//}
//
//- (void)_mttFinishInteractiveTransitionWithTranslation:(CGPoint) translation
//{
//    DefineWeakSelfBeforeBlock();
//    UIView *fromView = _dismissFromView;
//    UIView *toView = _dismissToView;
//    if (fromView && toView) {
//        CGRect targetRect;
//        if(self.animationStyle == ABCViewControllerPresentionAnimationFlipFromRight)//左右方向
//        {
//            targetRect = CGRectMake(CGRectGetWidth(_dismissViewOldFrame), CGRectGetMinY(_dismissViewOldFrame), _dismissFromView.frame.size.width, _dismissFromView.frame.size.height);
//        }
//        else
//        {
//            targetRect = CGRectMake(CGRectGetMinX(_dismissViewOldFrame), CGRectGetHeight(_dismissViewOldFrame), CGRectGetWidth(_dismissViewOldFrame), CGRectGetHeight(_dismissViewOldFrame));
//        }
//
//        CGFloat fraction = translation.x / fromView.bounds.size.width;
//        CGFloat duration = (1 - fraction) * ABC_DEFAULT_ANIMATION_TIME;
//        CGFloat maskOpacity = 0.0;
//        [fromView animateToFrameForShowDefault:targetRect maskOpacity:maskOpacity duration:duration completionBlock:^{
//            DefineStrongSelfInBlock(self);
//            if (_backgroundView) {
//                [_backgroundView removeFromSuperview];
//            }
//            [self finishInteractiveTransition];
//        }];
//
//#ifdef ABC_USE_SCALE_ANIMATION_ON_VIEW_TRANSITION
//        [toView animateFromScale:_currentScale toScale:1.0 duration:duration completionBlock:^{
//            UIView *toView = _dismissToView;
//            [toView removeScaleAnimation];
//            toView.transform = CGAffineTransformIdentity;
//        }];
//#endif
//
//        [toView animateToOpacityDefault:1.0 duration:duration completionBlock:nil];
//    }
//}
//
//- (void)finishInteractiveTransition
//{
//    if (_view) {
//        [self.delegate onTransitionFinish:_view];
//    }
//
//    _dismissalGestureRecognizer = nil;
//}

@end


static char animation_key;
@implementation UIView(ABCEdgeGesture)
- (void)setPresentionStyle:(ABCViewPresentionAnimationStyle)style toView:(UIView*)toView delegate:(id<ABCViewAnimationControllerDelegate>) delegate {
    if (style) {
        ABCViewAnimationController *animationController = [[ABCViewAnimationController alloc] init];
        animationController.delegate = delegate;
        animationController.animationStyle = style;

        // [animationController configueGestureRecognizerForView:self toView:toView];

        objc_setAssociatedObject(self, &animation_key, animationController, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    }
}

- (void) setEnablePresentionStyle:(BOOL)enable {
    ABCViewAnimationController* controller = objc_getAssociatedObject(self, &animation_key);
    controller.animationEnable = enable;
}
@end
