#import <Foundation/Foundation.h>
#import "CAKeyframeAnimation+AHEasing.h"
#import "ABCBaseInteraction.h"
#import "ABCBaseAnimation.h"

#define ABCFadeAnimationDuration .3f

typedef NS_ENUM(NSUInteger, ABCAnimationCurveType) {
    CurveTypeLinear,
	CurveTypeQuadratic,
	CurveTypeCubic,
	CurveTypeQuartic,
	CurveTypeQuintic,
	CurveTypeSine,
	CurveTypeCircular,
	CurveTypeExpo,
	CurveTypeElastic,
	CurveTypeBack,
	CurveTypeOpera,
};

enum EasingMode
{
	EaseIn = 0,
	EaseOut,
	EaseInOut,
};

typedef NS_ENUM(NSUInteger, ABCAnimationEasingMode) {
    ABCAnimationEaseIn          = EaseIn,
    ABCAnimationEaseOut         = EaseOut,
    ABCAnimationEaseInOut       = EaseInOut,
};

@interface ABCAnimationCenter : NSObject
{
    AHEasingFunction                _function;
    
    NSUInteger                      _curve;
    ABCAnimationEasingMode          _easing;
}

+ (ABCAnimationCenter *)defaultCenter;
//渐隐渐显动画
+ (CATransition *) ABCFadeAnimationWithDuration:(CGFloat)duration;
+ (CATransition *) ABCFadeAnimation;

- (void)setDefaultFunction:(AHEasingFunction)easingFunction;
- (void)setDefaultFunctionWithCurve:(NSUInteger)curve easing:(ABCAnimationEasingMode)easing;

- (AHEasingFunction)defaultFunction;
- (AHEasingFunction)defaultFunctionWithEasing:(ABCAnimationEasingMode)easing;

- (NSUInteger)defaultCurve;
- (NSUInteger)defaultEasing;

+ (AHEasingFunction)functionWithCurve:(ABCAnimationCurveType)curve easing:(ABCAnimationEasingMode)easing;

@end


@interface UINavigationController (ABCAnimationForIOS7)

@property(nonatomic,retain) ABCBaseAnimation * animationExtension;
@property(nonatomic,retain) ABCBaseInteraction * interactionExtensioin;

@end
