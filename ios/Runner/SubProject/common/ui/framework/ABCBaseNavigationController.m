#import "ABCBaseNavigationController.h"
#import "ABCViewControllerAnimationController.h"
#import <objc/runtime.h>
#import "ABCBaseViewController.h"


static char animation_key;

@interface ABCBaseNavigationController () <UIGestureRecognizerDelegate, UINavigationControllerDelegate> {
    
}

@end

@implementation ABCBaseNavigationController

- (void)viewDidLoad {
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    
    self.interactivePopGestureRecognizer.delegate = self;
    self.delegate = self;
}

- (BOOL) prefersStatusBarHidden {
    if([self.topViewController respondsToSelector:@selector(prefersStatusBarHidden)])
        return [self.topViewController prefersStatusBarHidden];
    return [UIApplication sharedApplication].statusBarHidden;
}

- (UIStatusBarStyle) preferredStatusBarStyle {
    if([self.topViewController respondsToSelector:@selector(preferredStatusBarStyle)])
        return [self.topViewController preferredStatusBarStyle];
    return [UIApplication sharedApplication].statusBarStyle;
}

- (void)didReceiveMemoryWarning {
    [super didReceiveMemoryWarning];
    // Dispose of any resources that can be recreated.
}

- (id)initWithRootViewController:(UIViewController *)rootViewController {
    self = [super initWithNavigationBarClass:[UINavigationBar class] toolbarClass:[UIToolbar class]];
    if (self) {
        [self setViewControllers:@[rootViewController]];
    }
    return self;
}

- (BOOL)shouldAutorotate
{
    return [self.topViewController shouldAutorotate];
}

- (UIInterfaceOrientationMask)supportedInterfaceOrientations {
    return UIInterfaceOrientationMaskPortrait;
}
    
//解决左滑卡死的问题
- ( BOOL)gestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    if ([self.visibleViewController isKindOfClass:[ABCBaseViewController class]]) {
        BOOL shouldBegin = [((ABCBaseViewController*)self.visibleViewController) screenEdgeGestureRecognizerShouldBegin:gestureRecognizer];
        if (!shouldBegin) {
            return NO;
        }
        
        return YES;
    }
    

    return NO;
}

//- (BOOL)gestureRecognizer:(UIGestureRecognizer *)gestureRecognizer shouldBeRequiredToFailByGestureRecognizer:(UIGestureRecognizer *)otherGestureRecognizer {
//    if (self.viewControllers.count > 0) {
//        if (self.topViewController != self.viewControllers[0]) {
//            return YES;
//        }
//    }
//
//    return NO;
//}

- (void)dealloc {
    self.interactivePopGestureRecognizer.delegate = nil;
    self.delegate = nil;
}

- (void)navigationController:(UINavigationController *)navigationController didShowViewController:(UIViewController *)viewController animated:(BOOL)animated {
    BOOL editing = viewController.isEditing;
    self.interactivePopGestureRecognizer.enabled = !editing;
}

- (void)pushViewController:(UIViewController *)viewController animated:(BOOL)animated {
    [super pushViewController:viewController animated:animated];
}

/*
#pragma mark - Navigation

// In a storyboard-based application, you will often want to do a little preparation before navigation
- (void)prepareForSegue:(UIStoryboardSegue *)segue sender:(id)sender {
    // Get the new view controller using [segue destinationViewController].
    // Pass the selected object to the new view controller.
}
*/


- (void)setPresentionStyle:(ABCViewControllerPresentionAnimationStyle)style {
    if (style) {
        ABCViewControllerAnimationController *animationController = [[ABCViewControllerAnimationController alloc] init];
        animationController.animationStyle = style;
        self.transitioningDelegate = animationController;
        objc_setAssociatedObject(self, &animation_key, animationController, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    } else {
        self.transitioningDelegate = nil;
    }
}
@end
