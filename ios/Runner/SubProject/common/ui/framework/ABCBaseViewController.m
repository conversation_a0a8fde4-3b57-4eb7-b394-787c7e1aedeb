#import "ABCBaseViewController.h"
#import "ABCViewControllerAnimationController.h"

#import <objc/runtime.h>


static char animation_key;
@interface ABCBaseViewController ()

@end

static __weak ABCBaseViewController* sCurrentViewController;
@implementation ABCBaseViewController


+ (ABCBaseViewController*) currentViewController {
    return sCurrentViewController;
}
- (id)init
{
    self = [super init];
    if (self) {
        sCurrentViewController = self;
    }
    return self;
}

-(void) dealloc {
    sCurrentViewController = nil;
}

- (BOOL)screenEdgeGestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    return YES;
}

- (void) setKeyboardPlaceHolderViewHeight:(CGFloat)height {
    
}

- (void)setPresentionStyle:(ABCViewControllerPresentionAnimationStyle)style {
    if (style) {
        ABCViewControllerAnimationController *animationController = [[ABCViewControllerAnimationController alloc] init];
        animationController.animationStyle = style;
        self.transitioningDelegate = animationController;
        objc_setAssociatedObject(self, &animation_key, animationController, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
    } else {
        self.transitioningDelegate = nil;
    }
}
@end
