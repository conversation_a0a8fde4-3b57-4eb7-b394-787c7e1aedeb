//
//  AbcBaseAppDelegate.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/8/6.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "AbcBaseAppDelegate.h"

@interface AbcBaseAppDelegate()

@property (nonatomic, strong) NSMutableArray* appDelegates;

//后台任务id,用于让程序切换后台后运行一小会时间
@property (nonatomic, assign) UIBackgroundTaskIdentifier backgroundTaskId;

@end


static AbcBaseAppDelegate* sInstance;
@implementation AbcBaseAppDelegate
- (instancetype)init
{
    self = [super init];
    if (self) {
        _appDelegates = [NSMutableArray array];
    }
    sInstance = self;
    return self;
}

+ (AbcBaseAppDelegate*)sharedInstance {
    return sInstance;
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:didFinishLaunchingWithOptions:)]) {
            [delegate application:application didFinishLaunchingWithOptions: launchOptions];
        }
    }
    
    return YES;
}




-(void) addApplicationDelegate:(id<UIApplicationDelegate>) delegate {
    [self.appDelegates addObject:delegate];
}



# pragma mark --UIApplicationDelegate
- (void)application:(UIApplication *)application didRegisterUserNotificationSettings:(UIUserNotificationSettings *)notificationSettings {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:didRegisterUserNotificationSettings:)]) {
            [delegate application:application didRegisterUserNotificationSettings: notificationSettings];
        }
    }
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(applicationDidEnterBackground:)]) {
            [delegate applicationDidEnterBackground:application];
        }
    }
    
    
    //进入后台开启一个空后台任务，允许运行一小会，解决聊天时，临时退出，解决后台保存数据问题
    self.backgroundTaskId = [application beginBackgroundTaskWithExpirationHandler:^{}];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(15 * NSEC_PER_SEC)), dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [self finishBackgroundTask:application];
    });
}

- (void) finishBackgroundTask:(UIApplication *)application {
    //回到前后台，结束后台任务
    if (self.backgroundTaskId != UIBackgroundTaskInvalid) {
        // If backgroundTaskId != UIBackgroundTaskInvalid, sharedApplication is always exist
        [application endBackgroundTask:self.backgroundTaskId];
        self.backgroundTaskId = UIBackgroundTaskInvalid;
    }
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(applicationDidBecomeActive:)]) {
            [delegate applicationDidBecomeActive:application];
        }
    }
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:didReceiveRemoteNotification:)]) {
            [delegate application:application didReceiveRemoteNotification:userInfo];
        }
    }
}

- (void)application:(UIApplication *)application didReceiveLocalNotification:(UILocalNotification *)notification {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:didReceiveLocalNotification:)]) {
            [delegate application:application didReceiveLocalNotification:notification];
        }
    }
}

- (void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:didRegisterForRemoteNotificationsWithDeviceToken:)]) {
            [delegate application:application didRegisterForRemoteNotificationsWithDeviceToken:deviceToken];
        }
    }
}


- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<NSString*, id> *)options {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:openURL:options:)]) {
            if ([delegate application:app openURL:url options:options])return YES;
        }
    }
    return NO;
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation {
    for (id<UIApplicationDelegate> delegate in self.appDelegates) {
        if([delegate respondsToSelector:@selector(application:openURL:sourceApplication:annotation:)]) {
            if ([delegate application:application openURL:url sourceApplication:sourceApplication annotation:annotation])return YES;
        }
    }
    return NO;
}



@end
