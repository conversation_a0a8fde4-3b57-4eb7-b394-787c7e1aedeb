
#import <UIKit/UIKit.h>
#import "ABCAnimationDefine.h"


/*This notification will be posted when the third invoke viewctl disappeared
 Given a chance for the app delegate to excute initial setup.
 */

@interface ABCBaseViewController : UIViewController

//该ViewController是否支持键盘resizemode模式
@property (nonatomic, assign) BOOL supportKeyboardReszeMode;



+ (ABCBaseViewController*) currentViewController;
- (void)setPresentionStyle:(ABCViewControllerPresentionAnimationStyle)style;
- (BOOL)screenEdgeGestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer;

- (void) setKeyboardPlaceHolderViewHeight:(CGFloat)height;


@end
