//
//  UIUtils.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "UIUtils.h"


@implementation UIUtils

+(UIViewController*) currentViewController {
    UIViewController* currentViewController = [self getRootViewController];

    BOOL runLoopFind = YES;
    while (runLoopFind) {
        if (currentViewController.presentedViewController) {
            currentViewController = currentViewController.presentedViewController;
        } else {
            if ([currentViewController isKindOfClass:[UINavigationController class]]) {
                currentViewController = ((UINavigationController *)currentViewController).visibleViewController;
            } else if ([currentViewController isKindOfClass:[UITabBarController class]]) {
                currentViewController = ((UITabBarController* )currentViewController).selectedViewController;
            } else {
                break;
            }
        }
    }
    
    return currentViewController;
}

+ (UIViewController *)getRootViewController{
    UIWindow* window = [[[UIApplication sharedApplication] delegate] window];
    NSAssert(window, @"The window is empty");
    return window.rootViewController;
}

+(UIImage*) loadImage:(NSString*) image {
    NSString* file = [[NSBundle mainBundle] pathForResource:[NSString stringWithFormat:@"images/%@.png", image] ofType:nil inDirectory:@"res"];
    return [UIImage imageWithContentsOfFile:file];
}

@end
