//
//  Macros.h
//  Runner
//
//  Created by f<PERSON>he on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "Log.h"
#import "Singleton.h"

#define DefineWeakVarBeforeBlock(var) \
__block __weak __typeof(var) __weak_##var = var

#define DefineStrongVarInBlock(var) \
__typeof(__weak_##var) var = __weak_##var

#define DefineWeakSelfBeforeBlock() \
__weak __typeof(self) __weak_self = self

#define DefineStrongSelfInBlock(strongSelf) \
__typeof(__weak_self) strongSelf = __weak_self



#ifndef dispatch_main_async_safe
#define dispatch_main_async_safe(block)\
if ([NSThread isMainThread]) {\
block();\
} else {\
dispatch_async(dispatch_get_main_queue(), block);\
}


#ifndef InvokeError
#define InvokeError(c, message) [NSError errorWithDomain:@"HippyInvokeError" code:c userInfo:@{ \
       @"code":@(c), \
       @"message":(message) \
   }]

#endif


#define GeneralHippyInvokeCallbackHandler  ^(NSObject * _Nullable data, NSError * _Nullable error) { \
    if (error) { \
        reject(@"-1", error.description, error); \
        return; \
    } \
    resolve(data); \
}

#endif
