//
//  PluginUtils.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/8/4.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "PluginUtils.h"
#import "FileUtils.h"
#import "AppInfo.h"
#import "ABCMacros.h"
#import "Preferences.h"

#import <UIKit/UIKit.h>


static const NSString* grayflagSuffix = @"_gray";

static const NSString* preflagSuffix = @"_pre";

@implementation PluginUtils
+ (NSString*) preparePlugin:(NSString*) pluginName mvIfTmp:(BOOL) mvIfTmp {
    /**
     * 插件可能存放在三个位置，内置插件，正常下载后的插件， 下载后解压到临时目录中的插件，今次检测这个三个位置，取版本最大的一个
     */
    NSString* downloadPluginsRootDir = [NSString stringWithFormat:@"%@/plugins", [FileUtils getAppDirectory:NSDocumentDirectory]];
    
    //内置插件目录
    NSString* embededPlugin =[[NSBundle mainBundle] pathForResource:[NSString stringWithFormat:@"plugins/%@", pluginName] ofType:nil inDirectory:@"res"];
    
    NSString* grayFlag = [[NSUserDefaults standardUserDefaults] objectForKey:PREFS_HIPPY_GRAY_FLAG];
    
    BOOL isGray = [grayFlag hasPrefix:@"gray"];
    BOOL isPre= [grayFlag hasPrefix:@"pre"];
    
    NSString* graySuffix = isGray ? grayflagSuffix: (isPre ? preflagSuffix : @"");
    
    //正常下载的插件目录
    NSString* downloadPluginDir = [NSString stringWithFormat:@"%@/%@%@", downloadPluginsRootDir, pluginName, graySuffix];
    
    //下载的插件的临时目录，（主插件当前正在使用情况，无法直接替换，所以先生成了临时目录)
    NSString* downloadPluginTmpDir = [NSString stringWithFormat:@"%@/%@%@_tmp", downloadPluginsRootDir, pluginName, graySuffix];
    NSArray* array = [NSArray arrayWithObjects:embededPlugin,downloadPluginDir, downloadPluginTmpDir, nil];
    NSInteger maxVersion = -1;
    NSString* rootDir;
    for (NSString* dir in array) {
        NSInteger version = [self getPluginVersion:dir];
        if (version > maxVersion) {
            maxVersion = version;
            rootDir = dir;
        }
    }
    
    //如果路径是以tmp结尾，说明是下载解压的插件，由于之前运行过程中更新不能直接更新到原目录，先解压到临时目录中
    //在这里,如果指定mvIfTmp，则将其移到指定目录，完成整个升级过程
    NSString* tmpSuffix = @"_tmp";
    //以tmp 结尾
    if ([rootDir hasSuffix:tmpSuffix] && mvIfTmp) {
        NSString* rootDirWithoutTmpSuffix = [rootDir substringToIndex:rootDir.length - tmpSuffix.length];
        NSError* error = nil;
        BOOL ret = [FileUtils moveFileAtPath:rootDir toPath:rootDirWithoutTmpSuffix error:&error];
        if (error == nil && ret) {
            rootDir = rootDirWithoutTmpSuffix;
            Log(@"rootDirWithoutTmpSuffix =%@", rootDirWithoutTmpSuffix);
        }
    }
    return rootDir;
}

/**
 * 获取插件版本号，如果插件不适合当前host程序，则返回-1
 */
+ (NSInteger) getPluginVersion:(NSString*)rootDir {
    NSString* confFile = [NSString stringWithFormat:@"%@/conf.json", rootDir];
    if (![FileUtils isFileExisted:confFile])
        return -1;
    
    NSError* error = nil;
    NSData* data= [NSData dataWithContentsOfFile:confFile];
    
    NSDictionary* dic = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];
    NSString* hostMinVersion = [dic objectForKey:@"hostMinVersion"];
    NSString* hostMaxVersion = [dic objectForKey:@"hostMaxVersion"];
    NSString* version = [dic objectForKey:@"version"];
    
    NSString* appVersion = [AppInfo appVersion];


    NSInteger hostMinVersionNum = [self versionToNum:hostMinVersion];
    NSInteger hostMaxVersionNum = [self versionToNum:hostMaxVersion];
    NSInteger versionNum = [self versionToNum:version];
    NSInteger appVersionNum = [self versionToNum:appVersion];
    
    if (appVersionNum >= hostMinVersionNum && appVersionNum <= hostMaxVersionNum)
        return versionNum;

    return -1;
}


+ (NSInteger) versionToNum:(NSString*)version {
    NSString* numVersion = [version stringByReplacingOccurrencesOfString:@"." withString:@""];
    NSInteger versionInNum = [numVersion integerValue];
    
    return versionInNum;
}
@end
