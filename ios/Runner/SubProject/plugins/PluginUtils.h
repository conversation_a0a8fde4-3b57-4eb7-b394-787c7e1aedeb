//
//  PluginUtils.h
//  Runner
//
//  Created by fei<PERSON> on 2020/8/4.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//
//  插件操作工具类
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface PluginUtils : NSObject

/**
 * 做一些插件准备工作，会在内置，已下载，已下载tmp目录中选择版本最大的一个
 * 返回插件的根目录
 * @param pluginName 插件名
 * @param mvIfTmp 由于js bundle在运行时更新，不能直接删除到当前运行目录，需要先放到临时目录，这里是否需要将tmp目录移到正常目录
 *          程序刚启动时，可以指定这个值
 * @return 插件根目录
 */
+ (NSString*) preparePlugin:(NSString*) pluginName mvIfTmp:(BOOL) mvIfTmp;
@end

NS_ASSUME_NONNULL_END
