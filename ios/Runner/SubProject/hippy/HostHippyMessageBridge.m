//
//  FlutterHippyMessageBridge.m
//  Runner
//
//  Created by 何飞 on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "HostHippyMessageBridge.h"
#import "ABCMacros.h"


@interface HostHippyMessageBridge()
@property (nonatomic, strong) NSPointerArray* flutterListeners;
@property (nonatomic, strong) NSPointerArray* hippyListeners;

@end

@implementation HostHippyMessageBridge
DEF_SINGLETON(HostHippyMessageBridge);

- (instancetype)init
{
    self = [super init];
    if (self) {
        _flutterListeners = [NSPointerArray weakObjectsPointerArray];
    
    }
    return self;
}

-(void) addHostMessageListener: (id<IHostMessageListener>) listener {
    [self.flutterListeners addPointer:(__bridge void * _Nullable)(listener)];
}

-(void) removeHostMessageListener: (id<IHostMessageListener>) listener {
      NSInteger index = 0;
    for (index = 0; index < self.flutterListeners.count; ++index) {
        if ([self.flutterListeners pointerAtIndex:index] == (__bridge void * _Nullable)(listener))
            break;
    }

    if (index < self.flutterListeners.count) {
        [self.flutterListeners removePointerAtIndex:index];
    }
}

-(void) onHostMessage:(NSString*) messageName messageBody:(NSDictionary*) messageBody {
    for (id<IHostMessageListener> listener in self.flutterListeners) {
        [listener onHostMessage:messageName messageBody:messageBody];
    }
}
@end
