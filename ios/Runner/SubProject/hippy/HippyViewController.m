/*!
 * iOS SDK
 *
 * <PERSON><PERSON> is pleased to support the open source community by making
 * Hippy available.
 *
 * Copyright (C) 2019 THL A29 Limited, a Tencent company.
 * All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#import "HippyViewController.h"
#import "HippyRootView.h"
#import "DebugView.h"
#import "HostHippyMessageBridge.h"
#import "HippyEventDispatcher.h"
#import "HippyBundleURLProvider.h"
#import "HippyAssert.h"
#import "HippyKeyCommands.h"

//#import <IQKeyboardManager/IQKeyboardManager.h>
#import "IQKeyboardManager.h"
#import "Preferences.h"
#import "AppInfo.h"


#import <FBKVOController.h>
#import <Bugly/Bugly.h>


#define DefineWeakSelfBeforeBlock() \
__weak __typeof(self) __weak_self = self

#define DefineStrongSelfInBlock(strongSelf) \
__typeof(__weak_self) strongSelf = __weak_self

#define kDebugServerKey  @"hippy_debug_server_key"

@interface HippyViewController ()<IHostMessageListener, UIActionSheetDelegate, UIGestureRecognizerDelegate>
@property(strong, nonatomic) HippyRootView *hippyRootView;
@property(strong, nonatomic) UIView* keyboardPlaceHolder;

@property(strong, nonatomic) NSLayoutConstraint* keyboardHeightConstaint;
@end

@implementation HippyViewController{
       FBKVOController* _kvoController;
    
}
- (instancetype)init
{
    self = [super init];
    if (self) {
        NSString* debugServer = [[NSUserDefaults standardUserDefaults] objectForKey:kDebugServerKey];
        if (debugServer.length > 0)  {
            [self setDebugServer:debugServer save:false];
        }
        
        self.supportKeyboardReszeMode = YES;
        
        self.keyboardPlaceHolder = [[UIView alloc]initWithFrame:CGRectZero];
        self.keyboardPlaceHolder.translatesAutoresizingMaskIntoConstraints = NO;
        [self.view addSubview:self.keyboardPlaceHolder];

        
        self.keyboardHeightConstaint = [NSLayoutConstraint constraintWithItem:self.keyboardPlaceHolder
         attribute:NSLayoutAttributeHeight
         relatedBy:NSLayoutRelationEqual
            toItem:nil
         attribute:NSLayoutAttributeHeight
        multiplier:1
                                                                     constant:0];
        [self.view addConstraint:self.keyboardHeightConstaint];
        [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.keyboardPlaceHolder
                                                              attribute:NSLayoutAttributeLeading
                                                              relatedBy:NSLayoutRelationEqual
                                                                 toItem:self.view
                                                              attribute:NSLayoutAttributeLeading
                                                             multiplier:1
                                                               constant:0]];
        [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.keyboardPlaceHolder
                                                              attribute:NSLayoutAttributeWidth
                                                              relatedBy:NSLayoutRelationEqual
                                                                 toItem:self.view
                                                              attribute:NSLayoutAttributeWidth
                                                             multiplier:1
                                                               constant:0]];
        [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.keyboardPlaceHolder
                                                              attribute:NSLayoutAttributeBottom
                                                              relatedBy:NSLayoutRelationEqual
                                                                 toItem:self.view
                                                              attribute:NSLayoutAttributeBottom
                                                             multiplier:1
                                                               constant:0]];
        
        _kvoController = [[FBKVOController alloc]initWithObserver:self];
        
        [[HostHippyMessageBridge sharedInstance] addHostMessageListener:self];
    }
    return self;
}

- (void)setKeyboardPlaceHolderViewHeight:(CGFloat)keyboardPlaceHolderViewHeight {
    self.keyboardHeightConstaint.constant = keyboardPlaceHolderViewHeight;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    [self bindKeys];
    
//    self.navigationController.navigationBarHidden = YES;
    
    
    self.view.backgroundColor = [UIColor whiteColor];
    
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(keyboardWillShow:)
                                                 name:UIKeyboardWillShowNotification
                                               object:self.view.window];
    
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(keyboardWillHide:) name:UIKeyboardWillHideNotification object:self.view.window];
    
    
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];
    [center addObserver:self
               selector:@selector(appDidEnterBackground)
                   name:UIApplicationDidEnterBackgroundNotification
                 object:nil];
    [center addObserver:self
               selector:@selector(appWillEnterForeground)
                   name:UIApplicationWillEnterForegroundNotification
                 object:nil];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    
    [IQKeyboardManager sharedManager].enable = YES;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    [IQKeyboardManager sharedManager].enable = NO;
}

-(void)dealloc {
    [[HostHippyMessageBridge sharedInstance] removeHostMessageListener:self];
    
    [[NSNotificationCenter defaultCenter] removeObserver:self];
    HippySetFatalHandler(nil);
    
    MttHippySetExceptionHandler(nil);
}


- (void) loadBundle:(NSString*)rootDir debug:(BOOL)debug  params:(NSDictionary*)params{
    NSString *commonBundlePath = [NSString stringWithFormat:@"%@/vendor.ios.js", rootDir];
    NSString *businessBundlePath = [NSString stringWithFormat:@"%@/index.ios.js",rootDir];
    

    
    NSMutableDictionary *extendsLaunchOptions = [NSMutableDictionary new];
    [extendsLaunchOptions addEntriesFromDictionary: params];
    NSString* appVersion = [AppInfo appVersion];
    NSString* osVersion = [[UIDevice currentDevice] systemVersion];
    [extendsLaunchOptions setObject:appVersion forKey:@"appVersion"];
    [extendsLaunchOptions setObject:osVersion forKey:@"osVersion"];
    params = extendsLaunchOptions;
    
    DefineWeakSelfBeforeBlock();
    HippyBridge *bridge = nil;
    if (!debug) {
        bridge = [[HippyBridge alloc] initWithBundleURL:[NSURL fileURLWithPath:commonBundlePath]
                                                      moduleProvider:^NSArray<id<HippyBridgeModule>> *{
            DefineStrongSelfInBlock(sself);
            return [sself providers];
        } launchOptions:nil executorKey:nil];
        
        
        HippySetFatalHandler(^(NSError* error){
            NSString *failReason = error.localizedFailureReason;
            NSString *name = [NSString stringWithFormat:@"HippyFatalException: %@", error.localizedDescription];
            NSString *stack = HippyFormatError(error.localizedDescription, error.userInfo[@"HippyJSStackTraceKey"], 75);
            [Bugly reportExceptionWithCategory:5 name:name reason:failReason callStack:[stack componentsSeparatedByString:@"\n"] extraInfo:@{} terminateApp:NO];
        });
        
        MttHippySetExceptionHandler(^(NSException* exception) {
            NSString *name = [NSString stringWithFormat:@"HippyFatalException: %@", exception.description];
            NSString *stack = HippyFormatError(exception.description, exception.userInfo[@"HippyJSStackTraceKey"], 75);
            [Bugly reportExceptionWithCategory:5 name:name reason:exception.description callStack:[stack componentsSeparatedByString:@"\n"] extraInfo:@{} terminateApp:NO];
        });
    }
    


    HippyRootView *rootView = [[HippyRootView alloc] initWithBridge:bridge businessURL:[NSURL fileURLWithPath:businessBundlePath] moduleName:@"abcyun"
                                                  initialProperties: params
                                                      launchOptions:nil shareOptions:nil debugMode:debug delegate:nil];

    
    HippyRootView* oldHippyRootView = self.hippyRootView;
    
    self.hippyRootView = rootView;
    rootView.translatesAutoresizingMaskIntoConstraints = NO;
        
    rootView.frame = self.view.bounds;
    [self.view addSubview:rootView];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.hippyRootView
                                                          attribute:NSLayoutAttributeTop
                                                          relatedBy:NSLayoutRelationEqual
                                                             toItem:self.view
                                                          attribute:NSLayoutAttributeTop
                                                         multiplier:1
                                                           constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.hippyRootView
                                                          attribute:NSLayoutAttributeLeading
                                                          relatedBy:NSLayoutRelationEqual
                                                             toItem:self.view
                                                          attribute:NSLayoutAttributeLeading
                                                         multiplier:1
                                                           constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.hippyRootView
                                                          attribute:NSLayoutAttributeWidth
                                                          relatedBy:NSLayoutRelationEqual
                                                             toItem:self.view
                                                          attribute:NSLayoutAttributeWidth
                                                         multiplier:1
                                                           constant:0]];
    
    [self.view addConstraint:[NSLayoutConstraint constraintWithItem:self.hippyRootView
                                                          attribute:NSLayoutAttributeBottom
                                                          relatedBy:NSLayoutRelationEqual
                                                             toItem:self.keyboardPlaceHolder
                                                          attribute:NSLayoutAttributeTop
                                                         multiplier:1
                                                           constant:0]];

    
    
    if (debug) {
        DebugView* debugView = [[DebugView alloc] initWithFrame:CGRectMake(30,30, 30, 30)];
        [self.view addSubview:debugView];
        UITapGestureRecognizer *tap = [[UITapGestureRecognizer alloc]initWithTarget:self action:@selector(requestReload:) ];
        [debugView addGestureRecognizer:tap];
        
        [self.view addSubview:debugView];
        
        UILongPressGestureRecognizer *longPress = [[UILongPressGestureRecognizer alloc]initWithTarget:self action:@selector(showDevMenu:) ];
        [debugView addGestureRecognizer:longPress];
    }
    
    
    UIScreenEdgePanGestureRecognizer *screenGesture = [[UIScreenEdgePanGestureRecognizer alloc] initWithTarget:self action:@selector(handlePanGestureRecognizer:)];
    screenGesture.edges = UIRectEdgeLeft;
    [self.view addGestureRecognizer:screenGesture];
    
    screenGesture.delegate  = self;
    
    if (oldHippyRootView) {
        [oldHippyRootView removeFromSuperview];
        oldHippyRootView = nil;
    }
}


- (void)handlePanGestureRecognizer:(UIScreenEdgePanGestureRecognizer *)gestureRecognizer
{
    CGPoint translation = [gestureRecognizer translationInView:gestureRecognizer.view];
    CGPoint velocity = [gestureRecognizer velocityInView:gestureRecognizer.view];
    switch (gestureRecognizer.state) {
        case UIGestureRecognizerStateEnded:
        case UIGestureRecognizerStateCancelled:
        {
            if ((translation.x < self.view.bounds.size.width / 2 && velocity.x < 500) || gestureRecognizer.state == UIGestureRecognizerStateCancelled)
            {
            }
            else if (self.hippyRootView.bridge.onGestureBack)
            {
                self.hippyRootView.bridge.onGestureBack();
            }
            break;
        }
        default:
            break;
    }
}


- (void) requestReload:(id) view{
    [self.hippyRootView.bridge requestReload];
}

-( NSArray<id<HippyBridgeModule>>* ) providers {
    return NULL;
}


-(void) onHostMessage:(NSString*) messageName messageBody:(NSDictionary*) messageBody {
    [self.hippyRootView.bridge.eventDispatcher dispatchEvent:@"EventDispatcher" methodName:@"receiveNativeEvent"
                                                        args:@{@"eventName":messageName,
                                                               @"extra": messageBody ? : @{}}];
}


#define DEVICE_HEIGHT [[UIScreen mainScreen] bounds].size.height
-(void)keyboardWillShow:(NSNotification *)noti
{
    CGRect keyboardRect = [[noti.userInfo objectForKey:UIKeyboardFrameEndUserInfoKey]
                           CGRectValue];
    keyboardRect = [self.view convertRect:keyboardRect fromView:nil];
    CGFloat keyboarHeight = CGRectGetHeight(keyboardRect);

    
    [self emitKeyboardVisibleEvent:true keyboardHeight:keyboarHeight];
}

-(void)keyboardWillHide:(NSNotification *)noti
{
    
    CGRect keyboardRect = [[noti.userInfo objectForKey:UIKeyboardFrameEndUserInfoKey]
                           CGRectValue];
    keyboardRect = [self.view convertRect:keyboardRect fromView:nil];
    CGFloat keyboarHeight = CGRectGetHeight(keyboardRect);
    
    [self emitKeyboardVisibleEvent:false keyboardHeight:keyboarHeight];
}

- (void)appDidEnterBackground {
    [self.hippyRootView.bridge.eventDispatcher dispatchEvent:@"EventDispatcher" methodName:@"receiveNativeEvent"
                                                        args:@{@"eventName": @"appEnterBackground"}];
}

- (void)appWillEnterForeground {
    [self.hippyRootView.bridge.eventDispatcher dispatchEvent:@"EventDispatcher" methodName:@"receiveNativeEvent"
                                                        args:@{@"eventName": @"appEnterForeground"}];
}


- (void) emitKeyboardVisibleEvent:(BOOL) visible  keyboardHeight:(NSInteger) keyboardHeight{
    [self.hippyRootView.bridge.eventDispatcher dispatchEvent:@"EventDispatcher" methodName:@"receiveNativeEvent"
                                                        args:@{@"eventName": @"keyboardVisibleChanged",
                                                               @"extra": @{@"visible":@(visible),                                     @"keyboardHeight":@(keyboardHeight)
                                                        }}];
}

- (BOOL)screenEdgeGestureRecognizerShouldBegin:(UIGestureRecognizer *)gestureRecognizer {
    if (self.hippyRootView.bridge.screenEdgeGestureRecognizerEnable) {
        return self.hippyRootView.bridge.screenEdgeGestureRecognizerEnable();
    }
    
    return YES;
}



- (void)showDevMenu:(UIGestureRecognizer*)recognizer
{
    if(recognizer.state == UIGestureRecognizerStateBegan) {
        UIActionSheet *actionSheet = [[UIActionSheet alloc]
                                      initWithTitle:@"Hippy调试"
                                      delegate:self
                                      cancelButtonTitle:@"取消"
                                      destructiveButtonTitle:@"确定"
                                      otherButtonTitles:@"设置地址", @"重新加载",@"退出调试",nil];
        actionSheet.actionSheetStyle = UIBarStyleDefault;
        [actionSheet showInView:self.view];
    }
}


-(void)actionSheet:(UIActionSheet *)actionSheet clickedButtonAtIndex:(NSInteger)buttonIndex
{
    switch (buttonIndex) {
        case 1:
            [self requestSetDebugServer];
            break;
        case 2:
            [self.hippyRootView.bridge requestReload];
            break;
        case 3:
            [[NSUserDefaults standardUserDefaults] setObject: @(NO) forKey:PREFS_HIPPY_DEBUG_SWITCH];
            break;
        case 4:
            NSLog(@"取消");
            break;
        case 0:
            NSLog(@"确定");
            break;
        default:
            break;
    }
}

- (void) requestSetDebugServer {
    UIAlertController* alert = [UIAlertController alertControllerWithTitle:@"设置调试server地址"
                                                                   message:@""
                                                            preferredStyle:UIAlertControllerStyleAlert];
    
    UIAlertAction* okAction = [UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault
                                                     handler:^(UIAlertAction * action) {
        NSString* addr = alert.textFields[0].text;
        if (addr.length > 0) {
            [self setDebugServer:addr save:true];
        }
    }];
    UIAlertAction* cancelAction = [UIAlertAction actionWithTitle:@"Cancel" style:UIAlertActionStyleCancel
                                                         handler:^(UIAlertAction * action) {
    }];
    [alert addTextFieldWithConfigurationHandler:^(UITextField *textField) {
        textField.placeholder = @"server地址";
        HippyBundleURLProvider* provider = [HippyBundleURLProvider sharedInstance];
        if (provider.localhostIP.length > 0) {
            textField.text = [NSString stringWithFormat:@"%@:%@", provider.localhostIP, provider.localhostPort];
        }

    }];
    
    [alert addAction:okAction];
    [alert addAction:cancelAction];
    [self presentViewController:alert animated:YES completion:nil];
    
}

-(void) setDebugServer:(NSString*) addr save:(BOOL) save{
    if (addr.length > 0) {
        HippyBundleURLProvider* provider = [HippyBundleURLProvider sharedInstance];
        NSArray* arr= [addr componentsSeparatedByString:@":"];
        if (arr.count == 2) {
            [provider setLocalhostIP:arr[0] localhostPort:arr[1]];
        }
        else {
            [provider setLocalhostIP:arr[0] localhostPort:@"38989"];
        }
        
        if (save)
            [[NSUserDefaults standardUserDefaults] setObject:addr forKey:kDebugServerKey];
    }
}



- (void)bindKeys
{
    HippyAssertMainQueue();

#if TARGET_IPHONE_SIMULATOR
    HippyKeyCommands *commands = [HippyKeyCommands sharedInstance];
    // reload in current mode
    __weak typeof(self) weakSelf = self;
    [commands registerKeyCommandWithInput:@"r"
                            modifierFlags:UIKeyModifierControl
                                   action:^(__unused UIKeyCommand *command) {
                                        [weakSelf requestReload:nil];
                                   }];
#endif
}

@end
