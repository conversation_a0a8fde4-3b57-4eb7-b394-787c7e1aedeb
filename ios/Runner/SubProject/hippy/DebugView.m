//
//  DebugView.m
//  Runner
//
//  Created by feihe on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "DebugView.h"


@implementation DebugView {
    float oldX, oldY;
    BOOL dragging;
}

/*
// Only override drawRect: if you perform custom drawing.
// An empty implementation adversely affects performance during animation.
- (void)drawRect:(CGRect)rect {
    // Drawing code
}
*/

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = [UIColor blueColor];
        self.layer.cornerRadius = 15;
    }
    return self;
}


- (void)touchesBegan:(NSSet *)touches withEvent:(UIEvent *)event {
    UITouch *touch = [[event allTouches] anyObject];
    CGPoint touchLocation = [touch locationInView:self];
    
    if (CGRectContainsPoint(self.window.frame, touchLocation)) {
        dragging = YES;
        oldX = touchLocation.x;
        oldY = touchLocation.y;
    }
}

- (void)touchesMoved:(NSSet *)touches withEvent:(UIEvent *)event {

    UITouch *touch = [[event allTouches] anyObject];
    CGPoint touchLocation = [touch locationInView:self];

    if (dragging) {
        CGRect frame = self.frame;
        frame.origin.x = frame.origin.x + touchLocation.x - oldX;
        frame.origin.y =  frame.origin.y + touchLocation.y - oldY;
        self.frame = frame;
    }
}

@end
