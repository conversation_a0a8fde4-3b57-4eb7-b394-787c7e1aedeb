#import "AudioRecorderModule.h"
#import <AVFoundation/AVFoundation.h>
#import "AudioPlayerMananger.h"

@interface AudioRecorderModule()<AVAudioRecorderDelegate>
@end

@implementation AudioRecorderModule{
    NSString* mExtension;
    NSString* mPath;
    NSDate* startTime;
    AVAudioRecorder* audioRecorder;
    BOOL isRecording;
}

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_MODULE(AudioRecorder)

HIPPY_EXPORT_METHOD(start:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [[AudioPlayerMananger sharedInstance] pauseAll];
    [self start:params result:^(NSObject * _Nullable data, NSError * _Nullable error) {
        if (error) {
            reject(@"-1", error.description, error);
            return;
        }
        
        resolve(data);
    }];
}

HIPPY_EXPORT_METHOD(stop:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self stop:params result:GeneralHippyInvokeCallbackHandler];
}

HIPPY_EXPORT_METHOD(isRecording:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    resolve(@(isRecording));
}

HIPPY_EXPORT_METHOD(hasPermissions:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self hasPermissions:params result:^(NSObject * _Nullable data, NSError * _Nullable error) {
        if (error) {
            reject(@"-1", error.description, error);
            return;
        }
        
        resolve(data);
    }];
}


- (void) stop:(NSDictionary*) arguments result:(HippyMethodInvoideCallback) result {
    if (!isRecording) {
        result(nil, InvokeError(-1, @"在stop之前需要先start"));
        return;
    }
    
    [audioRecorder stop];
    audioRecorder = nil;
    NSInteger duration = (NSInteger)([[NSDate date] timeIntervalSinceDate:startTime] * 1000);
    isRecording = false;
    result(@{
        @"duration":@(duration),
        @"path": mPath,
        @"audioOutputFormat":mExtension
           }, nil);
}

- (void) start:(NSDictionary*) arguments result:(HippyMethodInvoideCallback) result {
    mExtension = [arguments objectForKey:@"extension"];
    if (mExtension == NULL) {
        mExtension = @"";
    }
    
    mPath = [arguments objectForKey:@"path"];
    if (mPath == NULL) {
        mPath = @"";
    }
    startTime = [NSDate date];
    if ([mPath isEqualToString:@""]) {
        NSString *documentsPath = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES)[0];
        mPath = [NSString stringWithFormat:@"%@/%d.m4a", documentsPath, (int)startTime.timeIntervalSince1970];
    }
    
    
    NSDictionary* settings = [NSDictionary dictionaryWithObjectsAndKeys:
                              [self getOutputFormatFromString:mExtension], AVFormatIDKey,
                              [NSNumber numberWithFloat:12000], AVSampleRateKey,
                              [NSNumber numberWithInt:1],AVNumberOfChannelsKey,
                              [NSNumber numberWithInt:AVAudioQualityHigh],AVEncoderAudioQualityKey,
                              nil];
    
    NSError* error = nil;
    [[AVAudioSession sharedInstance] setCategory:AVAudioSessionCategoryRecord error:&error];
    if (error !=nil) {
        result(nil, InvokeError(error.code, error.domain));
        return;
    }
    
    [[AVAudioSession sharedInstance] setActive:true error:&error];
    if (error !=nil) {
        result(nil, InvokeError(error.code, error.domain));
        return;
    }
    
    
    audioRecorder = [[AVAudioRecorder alloc] initWithURL:[NSURL URLWithString:mPath] settings:settings error:nil];
    audioRecorder.delegate = self;
    [audioRecorder record];
    
    isRecording = YES;
    result(@(YES), nil);
}

- (void) hasPermissions:(NSDictionary*) arguments result:(HippyMethodInvoideCallback) result {
    AVAudioSessionRecordPermission permission = [AVAudioSession sharedInstance].recordPermission;
    
    switch (permission) {
        case AVAudioSessionRecordPermissionGranted:
            result(@(YES), nil);
            break;
        case AVAudioSessionRecordPermissionDenied:
            result(@(NO),nil);
            break;
        case AVAudioSessionRecordPermissionUndetermined:
            
            [[AVAudioSession sharedInstance] requestRecordPermission:^(BOOL granted) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    result(@(granted), nil);
                });
            }];
            break;
    }
}

-(NSNumber*) getOutputFormatFromString:(NSString*) format {
    if ([@".mp4" isEqualToString:format] ||
        [@".aac" isEqualToString:format] ||
        [@".m4a" isEqualToString:format])
        return [NSNumber numberWithInt:kAudioFormatMPEG4AAC];
    
    return [NSNumber numberWithInt:kAudioFormatMPEG4AAC];
}

@end
