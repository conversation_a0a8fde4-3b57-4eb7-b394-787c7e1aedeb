//
//  ThirdCallModule.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "ThirdCallModule.h"
#import "Log.h"
#import "UIUtils.h"
#import "ABCMacros.h"
#import "HostHippyMessageBridge.h"

#import <SocketIO-Swift.h>



@implementation ThirdCallModule {

}
HIPPY_EXPORT_MODULE(ThirdCall)

- (instancetype)init
{
    self = [super init];
    if (self) {
    
    }
    
    return self;
}
- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

HIPPY_EXPORT_METHOD(register:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    resolve(@YES);
}

@end
