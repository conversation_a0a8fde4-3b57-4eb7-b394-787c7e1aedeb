#import "GallerySaverModule.h"
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>
#import "ABCMacros.h"
#import "HippyMethodInvoideCallback.h"

const NSString* kPath = @"path";
const NSString* kAlbumName = @"albumName";

enum MediaType {
    image,
    video,
};

@implementation GallerySaverModule
HIPPY_EXPORT_MODULE(GallerySaver)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_METHOD(saveImage:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self saveMedia:params mediaType:image result:GeneralHippyInvokeCallbackHandler];
}

HIPPY_EXPORT_METHOD(saveVideo:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self saveMedia:params mediaType:video result:GeneralHippyInvokeCallbackHandler];
}


- (void) saveMedia:(NSDictionary*)params mediaType: (enum MediaType)mediaType result:(HippyMethodInvoideCallback) result {
    NSString* path = [params objectForKey:kPath];
    NSString* albumName = [params objectForKey:kAlbumName];
    if ([albumName isKindOfClass:[NSNull class]]) {
        albumName = nil;
    }
    
    
    PHAuthorizationStatus status = [PHPhotoLibrary authorizationStatus];
    if (status == PHAuthorizationStatusNotDetermined) {
        [PHPhotoLibrary requestAuthorization: ^(PHAuthorizationStatus status){
            if (status == PHAuthorizationStatusAuthorized){
                [self _saveMediaToAlbum:path mediaType:mediaType albumName:albumName result:result];
            } else {
                result(nil, InvokeError(-1, @"无相册权限"));
            }
        }];
    } else if (status == PHAuthorizationStatusAuthorized) {
        [self _saveMediaToAlbum:path mediaType:mediaType albumName:albumName result:result];
    } else {
        result(nil, InvokeError(-1, @"无相册权限"));
    }
}

-(void) _saveMediaToAlbum:(NSString*)imagePath mediaType: (enum MediaType) mediaType  albumName:(NSString*)albumName result: (HippyMethodInvoideCallback)result {
    if(albumName == nil ){
        [self saveFile:imagePath mediaType:mediaType album:nil result:result];
    } else  {
        PHAssetCollection* album = [self fetchAssetCollectionForAlbum:albumName];
        if (album !=nil) {
            
            [self saveFile:imagePath mediaType:mediaType album:album result:result];
        }
        
        else {
            // create photos album
            [self createAppPhotosAlbum:albumName completion:^(NSError *error) {
                if (error != nil) {
                    result(@(NO), nil);
                }
                
                PHAssetCollection* album = [self fetchAssetCollectionForAlbum:albumName];
                if (album != nil) {
                    [self saveFile:imagePath mediaType:mediaType album:album result:result];
                }
                else{
                    result(@(YES), nil);
                }
            }];
        }
    }
}

-(void) saveFile: (NSString*)filePath mediaType: (enum MediaType)mediaType album: (PHAssetCollection*)album result: (HippyMethodInvoideCallback)result {
    
    NSURL* url = [NSURL fileURLWithPath:filePath];
    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
        PHAssetChangeRequest* assetCreationRequest = mediaType == image ?
        [PHAssetChangeRequest creationRequestForAssetFromImageAtFileURL:url]
        : [PHAssetChangeRequest creationRequestForAssetFromVideoAtFileURL:url];
        if (album != nil) {
            PHAssetCollectionChangeRequest* assetCollectionChangeRequest = [PHAssetCollectionChangeRequest changeRequestForAssetCollection:album];
            PHObjectPlaceholder* createdAssetPlaceholder = assetCreationRequest.placeholderForCreatedAsset;
            if (createdAssetPlaceholder ==nil)return;
            
            [assetCollectionChangeRequest addAssets:[NSArray arrayWithObject:createdAssetPlaceholder]];
        }
        
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        if (success) {
            result(@(YES),nil);
        } else {
            result(nil, InvokeError(-1, error.domain));
        }
    }];
}

- (PHAssetCollection*) fetchAssetCollectionForAlbum:(NSString*)albumName {
    PHFetchOptions* fetchOptions = [[PHFetchOptions alloc] init];
    fetchOptions.predicate = [NSPredicate predicateWithFormat:@"title = %@", albumName];
    
    PHFetchResult<PHAssetCollection *> * collection = [PHAssetCollection fetchAssetCollectionsWithType: PHAssetCollectionTypeAlbum subtype: PHAssetCollectionSubtypeAny options: fetchOptions];
    return collection.firstObject;
}




- (void) createAppPhotosAlbum:(NSString*)albumName completion: (void(^)(NSError* error)) completion {
    [[PHPhotoLibrary sharedPhotoLibrary] performChanges:^{
        [PHAssetCollectionChangeRequest creationRequestForAssetCollectionWithTitle:albumName];
    } completionHandler:^(BOOL success, NSError * _Nullable error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            completion(error);
        });
    }];
}
@end
