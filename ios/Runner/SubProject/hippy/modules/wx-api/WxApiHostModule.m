//
//  WxApiHostModule.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/8/13.
//
//


#import "WxApiHostModule.h"
#import "WXApiHandler.h"
#import "ABCMacros.h"
#import "StringUtil.h"

@implementation WxApiHostModule

HIPPY_EXPORT_MODULE(WxApi)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}



- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}



HIPPY_EXPORT_METHOD(registerApp:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString* appId = params[@"appId"];
    NSNumber* enableMTA = params[@"enableMTA"];
    
    if (appId.length == 0) {
        reject(@"-1", @"appId为空", nil);
        return;
    }
    
    [[WXApiHandler sharedInstance] registerApp:appId enableMTA:[enableMTA boolValue] callback:^(BOOL success, NSError *error) {
        if (error) {
            reject(@"-1", error.description, error);
            return;
        }
    }];
}




HIPPY_EXPORT_METHOD(isWeChatInstalled:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    [[WXApiHandler sharedInstance] checkWeChatInstallation:^(BOOL success, NSError *error) {
        if (error) {
            reject(@"-1", error.description, nil);
            return;
        }
        
        resolve(@(success));
    }];
}

HIPPY_EXPORT_METHOD(sendAuth:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    
    NSString* scope  = [params objectForKey:@"scope"];
    NSString* state  = [params objectForKey:@"state"];
    if (scope.length == 0 || state.length == 0) {
        reject(@"-1", [NSString stringWithFormat:@"参数错误 scope = %@, state = %@", scope, state], nil);
        return;
    }
    
    [[WXApiHandler sharedInstance] sendAuthRequestScope: scope state:state openId:nil
                                               callback: ^(SendAuthResp* authResp, NSError *error) {
        Log(@"sendAuthRequestScope callback = authResp = %@", authResp);
        if (error) {
            reject(@"-1", error.description, error);
            return;
        }
        
        if (authResp == nil) {
            reject(@"-1", @"sendAuthRequestScope authResp空", nil);
            return;
        }
        
        
        NSDictionary *result = @{
            @"description": authResp.description == nil ? @"" : authResp.description,
            @"errStr": authResp.errStr == nil ? @"" : authResp.errStr,
            @"errCode": @(authResp.errCode),
            @"type": @(authResp.type),
            @"country": authResp.country == nil ? @"" : authResp.country,
            @"lang": authResp.lang == nil ? @"" : authResp.lang,
            @"code": [StringUtil nilToEmpty:authResp.code],
            @"state": [StringUtil nilToEmpty:authResp.state]
        };
        
        
        resolve(result);
    }];
}


HIPPY_EXPORT_METHOD(share:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *url = params[@"url"];
    NSString *title = params[@"title"];
    NSString *desc = params[@"desc"];
    NSString *iconUrl = params[@"iconUrl"];
    NSInteger sence= [((NSNumber*)params[@"scene"]) integerValue];
    
    NSString *transaction = params[@"transaction"]; //分享方式：webpage|img|miniProgram  default:webpage
    //兼容前端不上传的情况
    if ([transaction isEqual:nil]) {
        transaction = @"webpage";
    }
    
    NSDictionary *thumbSizeObj = params[@"thumbSize"];
    if ([thumbSizeObj isEqual:nil]) {
        thumbSizeObj = @{
            @"width": @128,
            @"height": @128,
        };
    }
    
//    小程序分享参数
    NSString *webpageUrl = params[@"webpageUrl"];
    NSString *userName = params[@"userName"];
    NSString *path = params[@"path"];
    bool withShareTicket = [(params[@"withShareTicket"]) boolValue];
    
    if ([transaction isEqualToString:@"miniProgram"]) {
        [[WXApiHandler sharedInstance] shareWithMiniprogram:title desc:desc iconUrl:iconUrl scene:(enum WXScene)sence webpageUrl:webpageUrl userName:userName path:path withShareTicket:(BOOL*)withShareTicket callback:^(BOOL success, NSError *error) {
            if (error) {
                reject(@"-1", error.description, nil);
                return;
            }
            
            resolve(@(success));
        }];
    }else if([transaction isEqualToString:@"img"]) {
        [[WXApiHandler sharedInstance] shareWithImage:iconUrl scene: (enum WXScene)sence callback:^(BOOL success, NSError *error) {
            if (error) {
                reject(@"-1", error.description, nil);
                return;
            }
            
            resolve(@(success));
        }];
    } else {
        [[WXApiHandler sharedInstance] shareWithUrl:url title:title desc:desc iconUrl:iconUrl scene: (enum WXScene)sence callback:^(BOOL success, NSError *error) {
            if (error) {
                reject(@"-1", error.description, nil);
                return;
            }
            
            resolve(@(success));
        }];
    }
    
    
//    [[WXApiHandler sharedInstance] shareWithUrl:url title:title desc:desc iconUrl:iconUrl scene: (enum WXScene)sence callback:^(BOOL success, NSError *error) {
//        if (error) {
//            reject(@"-1", error.description, nil);
//            return;
//        }
//        
//        resolve(@(success));
//    }];
}

HIPPY_EXPORT_METHOD(payment:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *prepayId = params[@"prepayId"];
    NSString *partnerId = params[@"partnerId"];
    NSString *packageValue = params[@"packageValue"];
    NSString *  nonceStr = params[@"nonceStr"];
    NSString *timestamp = params[@"timestamp"];
    NSString *sign = params[@"sign"];
    
    [[WXApiHandler sharedInstance] paymentWithPartnerId:partnerId prepayId:prepayId nonceStr:nonceStr timeStamp:[timestamp intValue] package:packageValue sign:sign callback:^(PayResp* result, NSError *error) {
        if (error) {
            reject(@"-1", error.description, nil);
            return;
        }
        
        NSMutableDictionary<NSString*, NSObject*>* resultMap = [NSMutableDictionary dictionary];
        [resultMap setValue:@(result.errCode) forKey:@"errCode"];
        [resultMap setValue:prepayId forKey:@"prepayId"];
        
        if (result.errStr)
            [resultMap setValue:result.errStr forKey:@"errStr"];
        
        if (result.returnKey)
            [resultMap setValue:result.returnKey forKey:@"returnKey"];
    
        resolve(resultMap);
    }];
}

HIPPY_EXPORT_METHOD(launchMiniProgram:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *userName = params[@"userName"];
    NSString *path = params[@"path"];
    NSNumber *_miniProgramType = params[@"miniProgramType"];
    
    WXMiniProgramType miniProgramType = WXMiniProgramTypeRelease;
    
    if ([_miniProgramType isEqualToNumber:@1])
    {
        miniProgramType = WXMiniProgramTypeTest;
    }
    else if ([_miniProgramType isEqualToNumber:@2])
    {
        miniProgramType = WXMiniProgramTypePreview;
    }
    
    [[WXApiHandler sharedInstance] launchMiniProgram:userName path:path miniProgramType:miniProgramType callback:^(BOOL success, NSError *error) {
        if (error) {
            reject(@"-1", error.description, nil);
            return;
        }
        
        resolve(@(success));
    }];
    
}

@end
