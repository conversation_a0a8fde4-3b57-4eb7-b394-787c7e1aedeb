//
//  WxApiHostModule.h
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/7/9.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//
//  给前端提供调用终端的能力，针对一些没有单独成一个模块的native能力接口，统一走这个接口

#import <UIKit/UIKit.h>
#import "ABCMacros.h"
#import "HippyMethodInvoideCallback.h"

#import "HippyBridgeModule.h"



NS_ASSUME_NONNULL_BEGIN

@interface HippyMixInvokeMethodModule : NSObject<HippyBridgeModule>

@end

NS_ASSUME_NONNULL_END
