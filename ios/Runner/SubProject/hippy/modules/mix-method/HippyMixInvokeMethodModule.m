//
//  HippyMixInvokeMethodModule.m
//  Runner
//
//  Created by feihe on 2020/7/9.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//
//


#import <UIKit/UIKit.h>
#import "HippyMixInvokeMethodModule.h"
#import "ABCMacros.h"
#import "StringUtil.h"
#import "BarcodeScanManager.h"
#import "Preferences.h"
#import "AppDelegate.h"
#import "FileUtils.h"
#import "UrlLanucherUtils.h"
#import <SSZipArchive.h>


@implementation HippyMixInvokeMethodModule

HIPPY_EXPORT_MODULE(MixInvokeMethod)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}


HIPPY_EXPORT_METHOD(invokeMethod:(NSString *)methodName params:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    [self doInvokeMethod:methodName params:params callback:GeneralHippyInvokeCallbackHandler];
}

-(void)doInvokeMethod:(NSString *)methodName params:(NSDictionary *)params callback:(HippyMethodInvoideCallback)callback {
    if ([methodName isEqualToString:@"BarcodeScanner_scan"]) {
        //扫码
        NSString* title = [params objectForKey:@"title"];
        NSNumber* selectCount = [params objectForKey:@"selectCount"];
        NSString* selectTips = [params objectForKey:@"selectTips"];
        
        [[BarcodeScanManager sharedInstance] scan:title selectCount:[selectCount integerValue] selectTips:selectTips callback:^(NSString *action, NSString *barCode, NSString *error) {
            
            if (callback) {
                NSMutableDictionary* result = [NSMutableDictionary dictionary];
                if (action)
                    [result setObject:action forKey:@"action"];
                
                if (barCode){
                    [result setObject:barCode forKey:@"barCode"];
                }
                if (error){
                    [result setObject:barCode forKey:@"error"];
                }
                
                callback(result, nil);
            }
        }];
    }
    else if ([methodName isEqualToString:@"setHippyDebug"]) {
        NSNumber* enableNum = [params objectForKey:@"enable"];
        [[NSUserDefaults standardUserDefaults] setObject:enableNum forKey:PREFS_HIPPY_DEBUG_SWITCH];
        callback(nil, nil);
    }
    else if ([methodName isEqualToString:@"setLogDebugLevel"]) {
        NSNumber* level = [params objectForKey:@"level"];
        [[NSUserDefaults standardUserDefaults] setObject:level forKey:PREFS_HIPPY_LOG_LEVEL];
    }
    else if ([methodName isEqualToString:@"reload"]) {
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        [appDelegate reload];
    }
    else if ([methodName isEqualToString:@"getAssetAsString"]) {
        NSString* asset = [params objectForKey:@"asset"];
        if (asset == nil)  {
            callback(nil, InvokeError(-1, @"缺少参数 asset"));
            return;
        }
        
        NSString *file = [[NSBundle mainBundle] pathForResource:asset ofType:nil inDirectory:@"res"];
        dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            NSError* error = nil;
            NSString *content = [NSString stringWithContentsOfFile:file encoding:NSUTF8StringEncoding error:&error];
            if (error == nil)
                callback(content, nil);
            else
                callback(nil, error);
        });
    }
    
    else if ([methodName isEqualToString:@"md5WithFile"]) {
        NSString* file = [params objectForKey:@"file"];
        if (file == nil)  {
            callback(nil, InvokeError(-1, @"缺少参数 file"));
            return;
        }
        dispatch_sync(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            NSString* md5 = [FileUtils fileMD5:file];
            if (callback)
                callback(md5, nil);
        });
    }
    else if ([methodName isEqualToString:@"unzip"]) {
        NSString* zipFile = [params objectForKey:@"zipFile"];
        NSString* outDir = [params objectForKey:@"outDir"];
        NSError* error = nil;
        if ([FileUtils isDirectoryExisted:outDir]) {
            [FileUtils deleteDirectory:outDir error:error];
        }
        
        if (zipFile == nil)  {
            callback(nil, InvokeError(-1, @"缺少参数 zipFile"));
            return;
        }
        if (outDir == nil)  {
            callback(nil, InvokeError(-1, @"缺少参数 outDir"));
            return;
        }
        
        dispatch_sync(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
            // Unzip
            BOOL ret = [SSZipArchive unzipFileAtPath:zipFile toDestination:outDir];
            if (callback)
                callback(@(ret), nil);
        });
    }
    else if ([methodName isEqualToString:@"launch"]) {
        NSString* url = [params objectForKey:@"url"];
        NSNumber* universalLinksOnly = [params objectForKey:@"universalLinksOnly"];

        [UrlLanucherUtils launchURL:url
                 universalLinksOnly:universalLinksOnly?universalLinksOnly.boolValue:NO
                  completionHandler:^(BOOL success) {
            if (callback) {
                callback(@(YES), nil);
            }
        }];
    }
    else if ([methodName isEqualToString:@"getImageSize"]) {
        NSString* path = [params objectForKey:@"path"];
        if (path.length == 0)  {
            callback(nil, InvokeError(-1, @"path为空"));
            return;
        }
        
        UIImage* image= [UIImage imageWithContentsOfFile:path];
        if (!image) {
            callback(nil, InvokeError(-1, @"图像解码失败"));
            return;
        }
        callback(@{
            @"width":@(image.size.width),
            @"height":@(image.size.height),
                 }, nil);
    }
    else if ([methodName isEqualToString:@"exitApp"]) {
        exit(0);
    }
    else if ([methodName isEqualToString:@"setGrayFlag"]) {
        NSString* grayFlag = [params objectForKey:@"grayFlag"];
        [[NSUserDefaults standardUserDefaults] setObject:grayFlag forKey:PREFS_HIPPY_GRAY_FLAG];
        callback(nil, nil);
    }
    else if ([methodName isEqualToString:@"hippyFirstFrameReady"]) {
        AppDelegate *appDelegate = (AppDelegate *)[[UIApplication sharedApplication] delegate];
        [appDelegate hippyFirstFrameShow];
        callback(nil, nil);
    }
    else {
        NSString* error = [NSString stringWithFormat:@"不支持方法: %@", methodName];
        callback(nil, InvokeError(-1, error));
    }
}
@end
