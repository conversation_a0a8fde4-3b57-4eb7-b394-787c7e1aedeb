// Copyright 2017 The Chromium Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.
#import "UrlLanucherUtils.h"
#import <UIKit/UIKit.h>

API_AVAILABLE(ios(9.0))
@interface UrlLanucherUtils ()
@end

@implementation UrlLanucherUtils

+ (BOOL)canLaunchURL:(NSString *)urlString {
  NSURL *url = [NSURL URLWithString:urlString];
  UIApplication *application = [UIApplication sharedApplication];
  return [application canOpenURL:url];
}

+(void) launchURL:(NSString*) urlString universalLinksOnly:(BOOL) universalLinksOnly completionHandler:(void (^ __nullable)(BOOL success))completion{
  NSURL *url = [NSURL URLWithString:urlString];
  UIApplication *application = [UIApplication sharedApplication];

  if (@available(iOS 10.0, *)) {
    NSDictionary *options = @{UIApplicationOpenURLOptionUniversalLinksOnly : @(universalLinksOnly)};
    [application openURL:url
                  options:options
        completionHandler:completion];
  } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
    BOOL success = [application openURL:url];
#pragma clang diagnostic pop
      
      if (completion)
          completion(success);
  }
}

@end
