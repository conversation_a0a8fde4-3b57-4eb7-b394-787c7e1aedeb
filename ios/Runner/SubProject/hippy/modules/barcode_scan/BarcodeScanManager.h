#import <Foundation/Foundation.h>
#import "ABCMacros.h"
#import "BarcodeScannerViewControllerDelegate.h"

#define UIColorFromRGB(rgbValue) \
[UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16))/255.0 \
  green:((float)((rgbValue & 0x00FF00) >>  8))/255.0 \
  blue:((float)((rgbValue & 0x0000FF) >>  0))/255.0 \
  alpha:1.0]


typedef void (^BarcodeScanCallback)(NSString* action, NSString* barCode, NSString* error);
@interface BarcodeScanManager : NSObject<BarcodeScannerViewControllerDelegate>

AS_SINGLETON(BarcodeScanManager);

- (void)scan:(NSString*) title selectCount:(NSInteger)count selectTips:(NSString*) selectTips callback:(BarcodeScanCallback) callback;

@end
