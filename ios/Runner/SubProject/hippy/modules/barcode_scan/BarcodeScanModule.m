//
//  Runner
//
//  Created by feihe on 2020/7/9.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//
//


#import "BarcodeScanModule.h"
#import "WXApiHandler.h"
#import "ABCMacros.h"
#import "StringUtil.h"
#import "BarcodeScanManager.h"

@implementation BarcodeScanModule

HIPPY_EXPORT_MODULE(BarcodeScan)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}



- (instancetype)init
{
    self = [super init];
    if (self) {
        
    }
    return self;
}


HIPPY_EXPORT_METHOD(scan:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    
    //扫码
    NSString* title = [params objectForKey:@"title"];
    NSNumber* selectCount = [params objectForKey:@"selectCount"];
    NSString* selectTips = [params objectForKey:@"selectTips"];
    
    [[BarcodeScanManager sharedInstance] scan:title selectCount:[selectCount integerValue] selectTips:selectTips callback:^(NSString *action, NSString *barCode, NSString *error) {
        NSMutableDictionary* result = [NSMutableDictionary dictionary];
        if (action)
            [result setObject:action forKey:@"action"];
        
        if (barCode){
            [result setObject:barCode forKey:@"barCode"];
        }
        if (error){
            [result setObject:error forKey:@"error"];
        }
        
        resolve(result);
    }];
}

@end
