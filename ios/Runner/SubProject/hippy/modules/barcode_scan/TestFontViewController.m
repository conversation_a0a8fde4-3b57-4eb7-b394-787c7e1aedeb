//#import "TestFontViewController.h"
//
//@interface TestFontViewController()
//
//@property(retain, nonatomic) UILabel* label1;
//
//@property(retain, nonatomic) UILabel* label2;
//
//@property(retain, nonatomic) UITextField* fontSize;
//
//
//@end
//
//@implementation TestFontViewController
//
//- (void)viewDidLoad {
//    [super viewDidLoad];
//    
//    self.view.backgroundColor = [UIColor whiteColor];
//    
//    self.navigationItem.leftBarButtonItem =  [[UIBarButtonItem alloc] initWithTitle:@"返回" style:UIBarButtonItemStylePlain target:self action:@selector(_back:)];
//    
//    _label1 = [[UILabel alloc] initWithFrame:CGRectMake(16, 100, 200, 32)];
//    _label1.font = [UIFont systemFontOfSize:14 weight:UIFontWeightMedium];
//    _label1.textColor = [UIColor blackColor];
//    _label1.text = @"阿莫西林克拉维林克拉维林";
//    [self.view addSubview:_label1];
//    
//    
//    
//    
//    _label2 = [[UILabel alloc] initWithFrame:CGRectMake(16, 150, 200, 32)];
//    _label2.font = [UIFont systemFontOfSize:16 weight:UIFontWeightMedium];
//    _label2.text = @"阿莫西林克拉维林克拉维林";
//    
//    
//    _label2.textColor = [UIColor blackColor];
//    [self.view addSubview:_label2];
//    
//    
//    _fontSize= [[UITextField alloc] initWithFrame:CGRectMake(16, 300, 200, 32)];
//    _fontSize.returnKeyType =UIReturnKeyDone;
//    _fontSize.text = @"16";
//    [self.view addSubview:_fontSize];
//    
//    UIButton* apply= [[UIButton alloc] initWithFrame:CGRectMake(16, 350, 100, 32)];
//    [apply addTarget:self action:@selector(_apply:) forControlEvents:UIControlEventTouchUpInside];
//    [apply setTitle:@"应用" forState:UIControlStateNormal];
//    [apply setTitleColor:[UIColor blackColor] forState:UIControlStateNormal];
//    [self.view addSubview:apply];
//}
//
//- (void)_back:(id)view{
//    [self dismissViewControllerAnimated:true completion:nil];
//}
//
//- (void) _apply:(id) view {
//    _label2.font = [UIFont systemFontOfSize:_fontSize.text.intValue weight:UIFontWeightMedium];
//}
//@end
