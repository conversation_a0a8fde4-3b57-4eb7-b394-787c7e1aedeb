#import "BarcodeScanManager.h"
#import "BarcodeScannerViewController.h"
#import "TestFontViewController.h"
#import "UIUtils.h"

@interface BarcodeScanManager()
@property(copy) BarcodeScanCallback callback;
@end

@implementation BarcodeScanManager

DEF_SINGLETON(BarcodeScanManager)

- (instancetype)init {
    self = [super init];
    if (self) {

    }
    return self;
}

- (void)scan:(NSString*) title selectCount:(NSInteger)count selectTips:(NSString*) selectTips callback:(BarcodeScanCallback)callback {
    BarcodeScannerViewController *scannerViewController = [[BarcodeScannerViewController alloc] init];
    BarcodeScannerNavigationController *navigationController = [[BarcodeScannerNavigationController alloc] initWithRootViewController:scannerViewController];
    navigationController.modalPresentationStyle = UIModalPresentationFullScreen;
    
    self.callback = callback;
    scannerViewController.delegate = self;
    scannerViewController.selectedTips = selectTips;
    scannerViewController.selectedCount = count;
    scannerViewController.title = title;

    [[UIUtils currentViewController] presentViewController:navigationController animated:NO completion:nil];
}

- (void)barcodeScannerViewController:(BarcodeScannerViewController *)controller didScanBarcodeWithResult:(NSString *)result {
    if (self.callback) {
        self.callback(nil, result, nil);
    }
}

- (void)barcodeScannerViewController:(BarcodeScannerViewController *)controller didFailWithErrorCode:(NSString *)errorCode {
    if (self.callback) {
        self.callback(nil, nil, errorCode);
    }
}


- (void)barcodeScannerViewController:(BarcodeScannerViewController *)controller didCancel:(NSString*)result {
    if (self.callback) {
        self.callback(@"user_cancel", nil, nil);
    }
}


- (void)barcodeScannerViewController:(BarcodeScannerViewController *)controller didOnViewScanListTap:(NSString*)result {
    if (self.callback) {
        self.callback(@"click_tips", nil, nil);
    }
}

@end
