#import "XGPushModule.h"
#import "AppDelegate.h"
#import <XGPush.h>
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= __IPHONE_10_0
#import <UserNotifications/UserNotifications.h>
#endif

#import "AbcBaseAppDelegate.h"
#import "HostHippyMessageBridge.h"


#define Log(fmt, ...)
//#define Log(fmt, ...) NSLog(fmt,##__VA_ARGS__,nil)

@interface XGPushModule () <XGPushDelegate>

@end

@implementation XGPushModule {
//    FlutterMethodChannel *_channel;
    
    NSDictionary *_launchNotification;
    NSDictionary *_launchLocalNotification;
    BOOL _resumingFromBackground;
}


- (instancetype)init
{
    self = [super init];
    if (self) {
        _resumingFromBackground = NO;
        [[AbcBaseAppDelegate sharedInstance] addApplicationDelegate:self];
    }
    return self;
}
HIPPY_EXPORT_MODULE(Push)



HIPPY_EXPORT_METHOD(start:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    dispatch_main_async_safe(^(){
        [self startWork:params result:^(NSObject * _Nullable data, NSError * _Nullable error) {
            if (error) {
                reject(@"-1", error.description,nil);
                return;
            }
            
            resolve(data);
        }];
    })
}


HIPPY_EXPORT_METHOD(getDeviceToken:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString* deviceToken = [[XGPushTokenManager defaultTokenManager] xgTokenString];
    dispatch_main_async_safe(^(){
        resolve(deviceToken);
    });
}

HIPPY_EXPORT_METHOD(getPushChannelId:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    dispatch_main_async_safe(^(){
        // 1 xg 2 oppo 3 vivo 4 tpns
        resolve(@4);
    });
}


-(void)registerWithAppDelegate:(id<UIApplicationDelegate>) delegate {
    [((AppDelegate*)delegate) addApplicationDelegate:self];
}


static NSString * const METHOD_ARENOTIFICATIONSENABLED = @"areNotificationsEnabled";
static NSString * const METHOD_OPENNOTIFICATIONSSETTINGS = @"openNotificationsSettings";
static NSString * const METHOD_STARTWORK = @"startWork";
static NSString * const METHOD_STOPWORK = @"stopWork";
static NSString * const METHOD_GETDEVICETOKEN = @"getDeviceToken";
static NSString * const METHOD_GETPUSHCHANNELID = @"getPushChannelId";
static NSString * const METHOD_BINDACCOUNT = @"bindAccount";
static NSString * const METHOD_UNBINDACCOUNT = @"unbindAccount";
static NSString * const METHOD_BINDTAGS = @"bindTags";
static NSString * const METHOD_UNBINDTAGS = @"unbindTags";

static NSString * const METHOD_ONRECEIVEDEVICETOKEN = @"onReceiveDeviceToken";
static NSString * const METHOD_ONRECEIVEMESSAGE = @"onReceiveMessage";
static NSString * const METHOD_ONRECEIVENOTIFICATION = @"onReceiveNotification";
static NSString * const METHOD_ONLAUNCHNOTIFICATION = @"onLaunchNotification";
static NSString * const METHOD_ONRESUMENOTIFICATION = @"onResumeNotification";

static NSString * const ARGUMENT_KEY_ENABLEDEBUG = @"enableDebug";
static NSString * const ARGUMENT_KEY_ACCOUNT = @"account";
static NSString * const ARGUMENT_KEY_TAGS = @"tags";

static NSString * const ARGUMENT_KEY_RESULT_TITLE = @"title";
static NSString * const ARGUMENT_KEY_RESULT_CONTENT = @"content";
static NSString * const ARGUMENT_KEY_RESULT_CUSTOMCONTENT = @"customContent";



- (void)openNotificationsSettings:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    NSURL *url = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
    if (@available(iOS 11.0, *)) {
        [[UIApplication sharedApplication] openURL:url options:@{} completionHandler:nil];
    } else {
        [[UIApplication sharedApplication] openURL:url];
    }
    result(@YES, nil);
}

- (void)startWork:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    [self requestNotificationsPermissionNotDetermined];
    NSNumber *enableDebug = call[ARGUMENT_KEY_ENABLEDEBUG];
    [[XGPush defaultManager] setEnableDebug:[enableDebug boolValue]];
    NSString *accessId = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"XG_ACCESS_ID"];
    NSString *accessKey = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"XG_ACCESS_KEY"];
    uint32_t accessIdUint32 = (uint32_t)[accessId longLongValue];
    [[XGPush defaultManager] startXGWithAccessID:accessIdUint32 accessKey:accessKey delegate:self];
    if (_launchNotification != nil) {
        [self didLaunchRemoteNotification:_launchNotification];
    }
    else if (_launchLocalNotification != nil) {
        [self didResumeRemoteNotification:_launchLocalNotification];
    }
    result(@YES, nil);
}

- (void)requestNotificationsPermissionNotDetermined {
    if (@available(iOS 10.0, *)) {
        [[UNUserNotificationCenter currentNotificationCenter] requestAuthorizationWithOptions:(UNAuthorizationOptionAlert + UNAuthorizationOptionSound + UNAuthorizationOptionBadge) completionHandler:^(BOOL granted, NSError * _Nullable error) {
        }];
    } else {
        UIUserNotificationType myTypes = UIUserNotificationTypeBadge | UIUserNotificationTypeSound | UIUserNotificationTypeAlert;
        UIUserNotificationSettings *settings = [UIUserNotificationSettings settingsForTypes:myTypes categories:nil];
        [[UIApplication sharedApplication] registerUserNotificationSettings:settings];
    }
}

- (void)stopWork:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    [[XGPush defaultManager] stopXGNotification];
    result(nil, nil);
}

- (void)bindAccount:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    NSString *account = call[ARGUMENT_KEY_ACCOUNT];
    [[XGPushTokenManager defaultTokenManager] appendAccounts:[NSArray arrayWithObject:account]];
    result(nil, nil);
}

- (void)unbindAccount:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    NSString *account = call[ARGUMENT_KEY_ACCOUNT];
    [[XGPushTokenManager defaultTokenManager] delAccounts:[NSArray arrayWithObject:account]];
    result(nil, nil);
}

- (void)bindTags:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    NSArray *tags = call[ARGUMENT_KEY_TAGS];
    [[XGPushTokenManager defaultTokenManager] appendTags:tags];

    result(nil,nil);
}

- (void)unbindTags:(NSDictionary*)call result:(HippyMethodInvoideCallback)result {
    NSArray *tags = call[ARGUMENT_KEY_TAGS];
    [[XGPushTokenManager defaultTokenManager] delTags:tags];
    result(nil, nil);
}

- (void)didLaunchRemoteNotification:(NSDictionary *)userInfo {
    Log(@"XGPushModule.didLaunchRemoteNotification, _channel = %@", _channel);
    NSDictionary *notification = [self parseNotification:userInfo];
    [self invokeMethod:METHOD_ONLAUNCHNOTIFICATION arguments:notification[ARGUMENT_KEY_RESULT_CUSTOMCONTENT]];
}

- (void)didResumeRemoteNotification:(NSDictionary *)userInfo {
    Log(@"XGPushModule.didResumeRemoteNotification");
    NSDictionary *notification = [self parseNotification:userInfo];
    [self invokeMethod:METHOD_ONRESUMENOTIFICATION arguments:notification[ARGUMENT_KEY_RESULT_CUSTOMCONTENT]];
}

- (void) invokeMethod:(NSString*) methodName arguments:(NSObject*) arguments {
        [[HostHippyMessageBridge sharedInstance] onHostMessage:@"Push" messageBody:@{
        @"methodName":methodName,
        @"args":arguments,
    }];
}

- (void)didReceiveRemoteNotification:(NSDictionary *)userInfo {
    Log(@"XGPushModule.didReceiveRemoteNotification");
    int contentAvailable = 0;
    NSDictionary *aps = userInfo[@"aps"];
    if ([aps objectForKey:@"content-available"]) {
        contentAvailable = [[NSString stringWithFormat:@"%@", aps[@"content-available"]] intValue];
    }
    if (contentAvailable == 1) {
        // 静默消息
        [self invokeMethod:METHOD_ONRECEIVEMESSAGE arguments:[self parseMessage:userInfo]];
    } else {
        // 通知推送
        [self invokeMethod:METHOD_ONRECEIVENOTIFICATION arguments:[self parseNotification:userInfo]];
    }
}

- (NSDictionary *)parseMessage:(NSDictionary *)userInfo {
    NSDictionary *alert = userInfo[@"aps"][@"alert"];
    
    NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
    dict[ARGUMENT_KEY_RESULT_TITLE] = alert[@"title"] ?: @"";
    dict[ARGUMENT_KEY_RESULT_CONTENT] = alert[@"body"] ?: @"";
    dict[ARGUMENT_KEY_RESULT_CUSTOMCONTENT] = [self parseCustomContent:userInfo] ?: @"";
    return dict;
}

- (NSDictionary *)parseNotification:(NSDictionary *)userInfo {
    Log(@"XGPushModule.parseNotification userInfo = %@", userInfo);
    NSDictionary *alert = userInfo[@"aps"][@"alert"];
    
    NSMutableDictionary *dict = [[NSMutableDictionary alloc] init];
    dict[ARGUMENT_KEY_RESULT_TITLE] = alert[@"title"] ?: @"";
    dict[ARGUMENT_KEY_RESULT_CONTENT] = alert[@"body"] ?: @"";
    dict[ARGUMENT_KEY_RESULT_CUSTOMCONTENT] = [self parseCustomContent:userInfo] ?: @"";
    return dict;
}

- (NSString *)parseCustomContent:(NSDictionary *)userInfo {
    NSMutableDictionary *customContent = [[NSMutableDictionary alloc] init];
    
    NSEnumerator *enumerator = [userInfo keyEnumerator];
    id key;
    while ((key = [enumerator nextObject])) {
        if (![key isEqual: @"xg"] && ![key isEqual: @"aps"]) {
            customContent[key] = userInfo[key];
        }
    }
    
    if (customContent.count == 0) {
        return nil;
    }
    
    //此处开始兼容之前老信鸽的处理逻辑,将custom里的url, playload提到与custom同一级上
    NSString* customJSONStr = [customContent objectForKey:@"custom"];
    if (customJSONStr) {
        NSDictionary* json = [NSJSONSerialization JSONObjectWithData:[customJSONStr dataUsingEncoding:NSUTF8StringEncoding] options:9 error:nil];
        
        const NSString* kURL  = @"url";
        NSString* url = [json objectForKey:kURL];
        if (url) {
            [customContent setObject:url forKey:kURL];
        }
        
        const NSString* kPayload  = @"payload";
        NSString* payload = [json objectForKey:kPayload];
        if (payload) {
            [customContent setObject:payload forKey:kPayload];
        }
    }

    NSError *error;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:customContent options:NSJSONWritingPrettyPrinted error:&error];
    if(!error) {
        return [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    }
    
    return nil;
}

# pragma mark - AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
    if (launchOptions != nil) {
        _launchNotification = launchOptions[UIApplicationLaunchOptionsRemoteNotificationKey];
        UILocalNotification *notification = launchOptions[UIApplicationLaunchOptionsLocalNotificationKey];
        
        if (notification)
            _launchLocalNotification = notification.userInfo;
    }
    
    Log(@"XGPushModule.application.didFinishLaunchingWithOptions, _launchNotification = %@, _launchLocalNotification= %@", _launchNotification, _launchLocalNotification);
    return YES;
}

- (void)application:(UIApplication *)application didRegisterUserNotificationSettings:(UIUserNotificationSettings *)notificationSettings {
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    _resumingFromBackground = YES;
}

- (void)applicationDidBecomeActive:(UIApplication *)application {
    _resumingFromBackground = NO;
}

- (void)application:(UIApplication *)application didReceiveRemoteNotification:(NSDictionary *)userInfo fetchCompletionHandler:(void (^)(UIBackgroundFetchResult))completionHandler {
    Log(@"XGPushModule.application.didReceiveRemoteNotification _resumingFromBackground = %d", _resumingFromBackground);
    
    if (_resumingFromBackground) {
        [self didResumeRemoteNotification:userInfo];
    } else {
        [self didReceiveRemoteNotification:userInfo];
    }
    completionHandler(UIBackgroundFetchResultNewData);
}

-(void)application:(UIApplication *)application didRegisterForRemoteNotificationsWithDeviceToken:(NSData *)deviceToken {
    NSString* token = [[NSString alloc] initWithData:deviceToken encoding:NSUTF8StringEncoding];
    NSLog(@"ios push device token: %@", token);
}

# pragma mark - XGPushDelegate

// iOS 10 新增 API
// iOS 10 会走新 API, iOS 10 以前会走到老 API
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= __IPHONE_10_0
// App 用户点击通知
// App 用户选择通知中的行为
// App 用户在通知中心清除消息
// 无论本地推送还是远程推送都会走这个回调
- (void)xgPushUserNotificationCenter:(UNUserNotificationCenter *)center didReceiveNotificationResponse:(UNNotificationResponse *)response withCompletionHandler:(void (^)(void))completionHandler __IOS_AVAILABLE(10.0) {
    [self didResumeRemoteNotification:response.notification.request.content.userInfo];
    // resume
    completionHandler();
}

// App 在前台弹通知需要调用这个接口
- (void)xgPushUserNotificationCenter:(UNUserNotificationCenter *)center willPresentNotification:(UNNotification *)notification withCompletionHandler:(void (^)(UNNotificationPresentationOptions))completionHandler __IOS_AVAILABLE(10.0) {
    [self didReceiveRemoteNotification:notification.request.content.userInfo];
    completionHandler(UNNotificationPresentationOptionBadge | UNNotificationPresentationOptionSound | UNNotificationPresentationOptionAlert);
}
#endif

-(void)xgPushDidRegisteredDeviceToken:(NSString *)deviceToken error:(NSError *)error {
    NSLog(@"xg push device token: %@", deviceToken);
    NSString* xgTokenString = [[XGPushTokenManager defaultTokenManager] xgTokenString];
    [self invokeMethod:METHOD_ONRECEIVEDEVICETOKEN arguments:xgTokenString];
}

@end
