#import <AliyunOSSiOS/OSSService.h>

@interface OssManager : NSObject
+ (OssManager*)instance;

@property(strong, nonatomic) NSString* endpoint;
@property(strong, nonatomic) OSSClient* ossClient;

- (void) initWithAccessKeyId:(NSString*)accessKeyId accessKeySecret: (NSString*)accessKeySecret
               securityToken: (NSString*) securityToken endpoint: (NSString*)endpoint initCallback:(dispatch_block_t)initCallback;
@end
