#import "OssManager.h"


static OssManager* sInstance;
@interface OssManager()
@property(strong) dispatch_queue_t workerQueue;
@end

@implementation OssManager
+ (OssManager*) instance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        sInstance = [[OssManager alloc] init];
    });
    return sInstance;
}

- (instancetype)init
{
    self = [super init];
    if (self) {
        self.workerQueue = dispatch_queue_create("OSSWorkerQueue", NULL);
    }
    return self;
}

- (void) initWithAccessKeyId:(NSString*)accessKeyId accessKeySecret: (NSString*)accessKeySecret
               securityToken: (NSString*) securityToken endpoint: (NSString*)endpoint initCallback:(dispatch_block_t)initCallback {
    self.endpoint = endpoint;
    
    dispatch_async(self.workerQueue, ^{
        // 由阿里云颁发的AccessKeyId/AccessKeySecret构造一个CredentialProvider。
        // 推荐使用OSSAuthCredentialProvider，token过期后会自动刷新。
        id<OSSCredentialProvider> credential = [[OSSStsTokenCredentialProvider alloc] initWithAccessKeyId:accessKeyId secretKeyId:accessKeySecret securityToken:securityToken];
        
        if (self.ossClient) {
            self.ossClient.credentialProvider = credential;
        }
        else {
            self.ossClient = [[OSSClient alloc] initWithEndpoint:self.endpoint credentialProvider:credential];
            }
        
        if (initCallback != nil) {
            initCallback();
        }
    });
}

@end
