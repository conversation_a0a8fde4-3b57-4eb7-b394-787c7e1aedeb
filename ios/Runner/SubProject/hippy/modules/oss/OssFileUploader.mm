#import <AliyunOSSiOS/OSSService.h>

#import "OssFileUploader.h"
#import "OssManager.h"
#import "ABCMacros.h"
#import "HostHippyMessageBridge.h"



@interface OssFileUploader()
@property(strong,nonatomic) NSString* bucketName;
@property(strong,nonatomic) NSString* objectKey;
@property(strong,nonatomic) NSString* file;
@property(strong,nonatomic) OSSTask* task;
@property(strong,nonatomic) OSSPutObjectRequest* putReq;

@property(strong,nonatomic) NSString* taskId;


@end

@implementation OssFileUploader

-(instancetype) initWithTaskId:(NSString*)taskId
                    bucketName:(NSString*)bucketName objectKey:(NSString*)objectKey file:(NSString*)file {
    Log(@"OssFileUploader init");
    if (self = [super init]){
        
        self.taskId =taskId;
        self.bucketName = bucketName;
        self.objectKey = objectKey;
        self.file = file;
    }
    
    return self;
}

- (void) cancel {
    [self.putReq cancel];
}

- (void) start {
    Log(@"OssFileUploader start");
    
    OSSPutObjectRequest * put = [OSSPutObjectRequest new];
    self.putReq = put;
    put.bucketName = self.bucketName;
    put.objectKey =self.objectKey;
    put.uploadingFileURL = [NSURL fileURLWithPath:self.file];
    // put.uploadingData = <NSData *>; // 直接上传NSData
    // 可选字段，可不设置
    put.uploadProgress = ^(int64_t bytesSent, int64_t totalByteSent, int64_t totalBytesExpectedToSend) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [self callbackInvokeMethod:@"onProgress" arguments: @{
                @"currentSize" : @(totalByteSent),
                @"totalSize" : @(totalBytesExpectedToSend)
            }];
        });
    };
    // 以下可选字段的含义参考： https://docs.aliyun.com/#/pub/oss/api-reference/object&PutObject
    // put.contentType = @"";
    // put.contentMd5 = @"";
    // put.contentEncoding = @"";
    // put.contentDisposition = @"";
    // put.objectMeta = [NSMutableDictionary dictionaryWithObjectsAndKeys:@"value1", @"x-oss-meta-name1", nil]; // 可以在上传时设置元信息或者其他HTTP头部
    self.task = [[OssManager instance].ossClient putObject:put];
    [self.task continueWithBlock:^id(OSSTask *task) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (!task.error) {
                Log(@"upload object success!");
                NSString* url = [NSString stringWithFormat:@"https://%@.%@/%@", self.bucketName, [OssManager instance].endpoint, self.objectKey];
                [self callbackInvokeMethod:@"onSuccess" arguments: @{
                    @"url" : url,
                }];
            } else {
                Log(@"upload object failed, error: %@" , task.error);
                [self  callbackInvokeMethod:@"onFailure" arguments: @{
                    @"error" : [NSString stringWithFormat:@"%@", task.error],
                }];
            }
        });
        
        
        return nil;
    }];
    // [putTask waitUntilFinished];
    // [put cancel];
}

-(void) callbackInvokeMethod:(NSString*) name arguments:(NSObject*) arguments {
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"OSSCallback" messageBody:@{
        @"taskId":self.taskId,
        @"methodName":name,
        @"args":arguments,
    }];
}
@end
