// Copyright 2019 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

#import "ImagePickerMetaDataUtil.h"
#import <Photos/Photos.h>

static const uint8_t kFirstByteJPEG = 0xFF;
static const uint8_t kFirstBytePNG = 0x89;
static const uint8_t kFirstByteGIF = 0x47;

NSString *const kImagePickerDefaultSuffix = @".jpg";
const ImagePickerMIMEType kImagePickerMIMETypeDefault = ImagePickerMIMETypeJPEG;

@implementation ImagePickerMetaDataUtil

+ (ImagePickerMIMEType)getImageMIMETypeFromImageData:(NSData *)imageData {
  uint8_t firstByte;
  [imageData getBytes:&firstByte length:1];
  switch (firstByte) {
    case kFirstByteJPEG:
      return ImagePickerMIMETypeJPEG;
    case kFirstBytePNG:
      return ImagePickerMIMETypePNG;
    case kFirstByteGIF:
      return ImagePickerMIMETypeGIF;
  }
  return ImagePickerMIMETypeOther;
}

+ (NSString *)imageTypeSuffixFromType:(ImagePickerMIMEType)type {
  switch (type) {
    case ImagePickerMIMETypeJPEG:
      return @".jpg";
    case ImagePickerMIMETypePNG:
      return @".png";
    case ImagePickerMIMETypeGIF:
      return @".gif";
    default:
      return nil;
  }
}

+ (NSDictionary *)getMetaDataFromImageData:(NSData *)imageData {
  CGImageSourceRef source = CGImageSourceCreateWithData((CFDataRef)imageData, NULL);
  NSDictionary *metadata =
      (NSDictionary *)CFBridgingRelease(CGImageSourceCopyPropertiesAtIndex(source, 0, NULL));
  CFRelease(source);
  return metadata;
}

+ (NSData *)updateMetaData:(NSDictionary *)metaData toImage:(NSData *)imageData {
  NSMutableData *mutableData = [NSMutableData data];
  CGImageSourceRef cgImage = CGImageSourceCreateWithData((__bridge CFDataRef)imageData, NULL);
  CGImageDestinationRef destination = CGImageDestinationCreateWithData(
      (__bridge CFMutableDataRef)mutableData, CGImageSourceGetType(cgImage), 1, nil);
  CGImageDestinationAddImageFromSource(destination, cgImage, 0, (__bridge CFDictionaryRef)metaData);
  CGImageDestinationFinalize(destination);
  CFRelease(cgImage);
  CFRelease(destination);
  return mutableData;
}

+ (NSData *)convertImage:(UIImage *)image
               usingType:(ImagePickerMIMEType)type
                 quality:(nullable NSNumber *)quality {
  if (quality && type != ImagePickerMIMETypeJPEG) {
    NSLog(@"image_picker: compressing is not supported for type %@. Returning the image with "
          @"original quality",
          [ImagePickerMetaDataUtil imageTypeSuffixFromType:type]);
  }

  switch (type) {
    case ImagePickerMIMETypeJPEG: {
      CGFloat qualityFloat = (quality != nil) ? quality.floatValue : 1;
      return UIImageJPEGRepresentation(image, qualityFloat);
    }
    case ImagePickerMIMETypePNG:
      return UIImagePNGRepresentation(image);
    default: {
      // converts to JPEG by default.
      CGFloat qualityFloat = (quality != nil) ? quality.floatValue : 1;
      return UIImageJPEGRepresentation(image, qualityFloat);
    }
  }
}

@end
