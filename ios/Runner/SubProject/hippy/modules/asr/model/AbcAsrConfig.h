//
//  AbcAsrConfig.h
//  AbcASR
//
//  ASR配置类，与Android端AsrConfig保持一致
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface AbcAsrConfig : NSObject

// WebSocket连接配置
@property (nonatomic, strong, nullable) NSString *serverUrl;
@property (nonatomic, strong, nullable) NSString *sessionId;
@property (nonatomic, assign) NSInteger connectTimeout;        // 连接超时时间（毫秒）
@property (nonatomic, assign) NSInteger readTimeout;           // 读取超时时间（毫秒）

// 音频配置
@property (nonatomic, assign) NSInteger sampleRate;            // 采样率
@property (nonatomic, assign) NSInteger channels;              // 声道数
@property (nonatomic, assign) NSInteger audioFormat;           // 音频格式（位深度）
@property (nonatomic, assign) NSInteger bufferSize;            // 缓冲区大小
@property (nonatomic, assign) BOOL enableAudioHeader;          // 是否启用音频数据头部标识

// 识别配置
@property (nonatomic, strong) NSString *language;              // 语言
@property (nonatomic, assign) BOOL enableVad;                  // 是否启用VAD
@property (nonatomic, assign) BOOL enablePunctuation;          // 是否启用标点符号
@property (nonatomic, assign) BOOL enableNumberConvert;        // 是否启用数字转换
@property (nonatomic, assign) BOOL enableDirtyFilter;          // 是否启用脏话过滤

// 静音检测配置
@property (nonatomic, assign) BOOL enableSilentDetection;      // 是否启用静音检测
@property (nonatomic, assign) NSInteger silentTimeout;         // 静音超时时间（毫秒）
@property (nonatomic, assign) float silentThreshold;           // 静音阈值（分贝）

// 音量回调配置
@property (nonatomic, assign) NSInteger volumeCallbackInterval; // 音量回调间隔（毫秒）

// 波形数据回调配置
@property (nonatomic, assign) NSInteger waveformCallbackInterval; // 波形数据回调间隔（毫秒）

// 前台服务配置
@property (nonatomic, assign) BOOL enableForegroundService;    // 是否启用前台服务
@property (nonatomic, strong) NSString *serviceNotificationTitle;   // 服务通知标题
@property (nonatomic, strong) NSString *serviceNotificationContent; // 服务通知内容

// Socket.IO连接配置
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *extraHeaders; // 额外的请求头
@property (nonatomic, strong, nullable) NSString *cookies;     // Cookie字符串
@property (nonatomic, strong) NSString *userAgent;             // 用户代理
@property (nonatomic, assign) BOOL withCredentials;            // 是否携带凭证
@property (nonatomic, strong, nullable) NSString *namespace;   // Socket.IO命名空间
@property (nonatomic, assign) NSInteger reconnectionDelay;     // 重连延迟时间（毫秒）
@property (nonatomic, assign) BOOL forceWebsockets;            // 是否强制使用WebSocket
@property (nonatomic, strong) NSString *socketPath;            // Socket.IO路径
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *connectParams; // 查询参数

// 初始化方法
- (instancetype)init;
- (instancetype)initWithServerUrl:(NSString *)serverUrl sessionId:(NSString *)sessionId;

// 添加额外请求头
- (void)addExtraHeader:(NSString *)key value:(NSString *)value;

// 从NSDictionary构建配置（用于Hippy参数解析）
+ (instancetype)configFromDictionary:(NSDictionary *)params sessionId:(NSString *)sessionId;

@end

NS_ASSUME_NONNULL_END