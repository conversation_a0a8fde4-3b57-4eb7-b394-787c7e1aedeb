//
//  AbcAsrModule.m
//  AbcASR
//
//  iOS ASR Hippy模块实现，对应Android的AbcAsrModule
//  提供ASR功能的JavaScript接口
//

#import "AbcAsrModule.h"
#import "AbcAsrManager.h"
#import "AbcAsrConfig.h"
#import "AbcAsrBackgroundHandler.h"
#import "HostHippyMessageBridge.h"

@interface AbcAsrModule () <AbcAsrManagerDelegate, AbcAsrBackgroundHandlerDelegate>

// 活跃会话管理
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *activeSessions;

// 直接模式的ASR管理器
@property (nonatomic, strong, nullable) AbcAsrManager *directAsrManager;

// 后台服务处理器
@property (nonatomic, strong, nullable) AbcAsrBackgroundHandler *backgroundHandler;

@property (nonatomic, strong, nullable) AbcAsrConfig *currentConfig;

@end

@implementation AbcAsrModule

HIPPY_EXPORT_MODULE(AbcASR)

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeSessions = [[NSMutableDictionary alloc] init];
        NSLog(@"[AbcAsrModule] AbcAsrModule initialized");
    }
    return self;
}

- (dispatch_queue_t)methodQueue {
    return dispatch_get_main_queue();
}

#pragma mark - Hippy Export Methods

/**
 * 初始化ASR连接
 */
HIPPY_EXPORT_METHOD(init:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSLog(@"[AbcAsrModule] Initializing ASR connection");

    @try {
        // 构建配置（使用临时sessionId）
        AbcAsrConfig *config = [AbcAsrConfig configFromDictionary:params sessionId:@"init-session"];
        self.currentConfig = config;

        // 根据配置选择初始化模式
        if (config.enableForegroundService) {
            // 初始化后台服务处理器
            self.backgroundHandler = [[AbcAsrBackgroundHandler alloc] initWithConfig:config];
            self.backgroundHandler.delegate = self;
            NSLog(@"[AbcAsrModule] ASR initialized in background service mode");
        }

        NSLog(@"[AbcAsrModule] Initializing direct mode");

        self.directAsrManager = [[AbcAsrManager alloc] initWithConfig:config];
        self.directAsrManager.delegate = self;

        // 在直接模式下，建立WebSocket连接但不开始录音
        BOOL success = [self.directAsrManager initConnection];
        if (success) {
            NSLog(@"[AbcAsrModule] ASR initialized in direct mode");
            resolve(@YES);
        } else {
            NSLog(@"[AbcAsrModule] Failed to initialize direct mode");
            reject(@"INIT_FAILED", @"直接模式初始化失败", nil);
        }

    } @catch (NSException *exception) {
        NSLog(@"[AbcAsrModule] Failed to initialize ASR: %@", exception.reason);
        reject(@"INIT_FAILED", [NSString stringWithFormat:@"初始化失败: %@", exception.reason], nil);
    }
}

/**
 * 开始语音识别
 */
HIPPY_EXPORT_METHOD(startRecognize:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *audioDataName = params[@"audioDataName"];

    // 生成唯一会话ID
    NSString *sessionId = [[NSUUID UUID] UUIDString];

    NSLog(@"[AbcAsrModule] Starting recognition with sessionId: %@", sessionId);

    @try {
        // 更新配置中的sessionId
        if (self.currentConfig) {
            self.currentConfig.sessionId = sessionId;
        }

        if (self.directAsrManager && self.currentConfig) {
            NSLog(@"[AbcAsrModule] Starting direct recording");
            BOOL success = [self.directAsrManager startRecording:audioDataName ?: @"audioData"];
            if (!success) {
                NSLog(@"[AbcAsrModule] Failed to start direct recording");
            }
        } else {
            NSLog(@"[AbcAsrModule] Direct ASR manager or config is nil");
        }

        // 添加到活跃会话
        [self.activeSessions setObject:sessionId forKey:sessionId];

        // 返回会话ID
        NSDictionary *resultMap = @{@"sessionId": sessionId};
        resolve(resultMap);

    } @catch (NSException *exception) {
        NSLog(@"[AbcAsrModule] Failed to start recognition: %@", exception.reason);
        reject(@"START_FAILED", [NSString stringWithFormat:@"启动识别失败: %@", exception.reason], nil);
    }
}

/**
 * 开始后台服务
 */
HIPPY_EXPORT_METHOD(startService:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    // 开始录音操作
    if (self.currentConfig && self.currentConfig.enableForegroundService && self.backgroundHandler) {
        // 开始ASR后台服务
        [self.backgroundHandler startService];
    }
    resolve(@YES);
}

/**
 * 停止后台服务
 */
HIPPY_EXPORT_METHOD(stopService:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    // 停止录音
    if (self.currentConfig && self.currentConfig.enableForegroundService && self.backgroundHandler) {
        // 停止ASR后台服务
        [self.backgroundHandler stopService];
    }
    resolve(@YES);
}

/**
 * 停止语音识别
 */
HIPPY_EXPORT_METHOD(stopRecognize:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *sessionId = params[@"sessionId"];

    if (!sessionId || sessionId.length == 0) {
        reject(@"INVALID_PARAMS", @"sessionId cannot be empty", nil);
        return;
    }

    NSLog(@"[AbcAsrModule] Stopping recognition for session: %@", sessionId);

    @try {
        if (self.directAsrManager) {
            NSLog(@"[AbcAsrModule] Stopping direct recording");
            [self.directAsrManager stopRecognition];
        }

        // 从活跃会话中移除
        [self.activeSessions removeObjectForKey:sessionId];

        resolve(@YES);
    } @catch (NSException *exception) {
        NSLog(@"[AbcAsrModule] Failed to stop recognition: %@", exception.reason);
        reject(@"STOP_FAILED", [NSString stringWithFormat:@"停止识别失败: %@", exception.reason], nil);
    }
}

/**
 * 发送Socket.IO消息
 */
HIPPY_EXPORT_METHOD(emitMessage:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *event = params[@"event"];
    NSDictionary *data = params[@"data"];

    if (self.directAsrManager) {
        [self.directAsrManager sendSocketMessage:event data:data];
    }
    resolve(@YES);
}

/**
 * 添加事件监听
 */
HIPPY_EXPORT_METHOD(eventOn:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *event = params[@"event"];

    if (self.directAsrManager) {
        [self.directAsrManager addEventListener:event];
    }
    resolve(@YES);
}

/**
 * 移除事件监听
 */
HIPPY_EXPORT_METHOD(eventOff:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSString *event = params[@"event"];

    if (self.directAsrManager) {
        [self.directAsrManager removeEventListener:event];
    }
    resolve(@YES);
}

/**
 * 开始来电监听
 */
HIPPY_EXPORT_METHOD(listenerCall:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    if (self.directAsrManager) {
        [self.directAsrManager startCallListener];
    }
    resolve(@YES);
}

/**
 * 释放资源
 */
HIPPY_EXPORT_METHOD(release:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    [self cleanup];
    resolve(@YES);
}

#pragma mark - AbcAsrManagerDelegate

- (void)asrManager:(id)manager didChangeState:(NSString *)state message:(NSString *)message {
    NSDictionary *stateData = @{
        @"state": state,
        @"message": message
    };
    [self invokeJSMethod:@"onStateChanged" params:stateData];
}

- (void)asrManager:(id)manager didReceiveError:(NSString *)errorCode message:(NSString *)errorMessage {
    NSDictionary *errorData = @{
        @"errorCode": errorCode,
        @"errorMessage": errorMessage
    };
    [self invokeJSMethod:@"onError" params:errorData];
}

- (void)asrManager:(id)manager didReceiveWaveformData:(NSArray<NSNumber *> *)waveformData {
    NSDictionary *waveformMap = @{
        @"data": waveformData
    };
    [self invokeJSMethod:@"onWaveformData" params:waveformMap];
}

- (void)asrManager:(id)manager didReceiveCallStateChange:(NSString *)state phoneNumber:(NSString *)phoneNumber {
    NSDictionary *callData = @{
        @"state": state,
        @"phoneNumber": phoneNumber ?: @""
    };
    [self invokeJSMethod:@"onCallStateChanged" params:callData];
}

- (void)asrManager:(id)manager didReceiveSocketEvent:(NSString *)event message:(NSArray *)items {
    NSString *message = @"";
    if (items.count > 0) {
        id firstItem = items[0];
        if ([firstItem isKindOfClass:[NSString class]]) {
            message = firstItem;
        } else if ([firstItem isKindOfClass:[NSDictionary class]] || [firstItem isKindOfClass:[NSArray class]]) {
            // 将NSDictionary或NSArray转换为JSON字符串
            NSError *error = nil;
            NSData *jsonData = [NSJSONSerialization dataWithJSONObject:firstItem
                                                               options:0
                                                                 error:&error];
            if (jsonData && !error) {
                message = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
            } else {
                NSLog(@"[AbcAsrModule] Failed to serialize to JSON: %@", error.localizedDescription);
                message = [firstItem description];
            }
        } else {
            message = [firstItem description];
        }
    }

    NSDictionary *socketData = @{
        @"event": event,
        @"message": message
    };

    NSLog(@"[AbcAsrModule] socketOn: %@ %@", event, message);
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"AbcASRSocketOn" messageBody:socketData];
}

#pragma mark - AbcAsrBackgroundHandlerDelegate

- (void)backgroundHandlerDidStartService:(id)handler {
    NSLog(@"[AbcAsrModule] Background service started");
}

- (void)backgroundHandlerDidStopService:(id)handler {
    NSLog(@"[AbcAsrModule] Background service stopped");
}

- (void)backgroundHandler:(id)handler didFailWithError:(NSError *)error {
    NSLog(@"[AbcAsrModule] Background service error: %@", error.localizedDescription);
    [self invokeJSMethod:@"onError" params:@{
        @"errorCode": @"BACKGROUND_SERVICE_ERROR",
        @"errorMessage": error.localizedDescription
    }];
}

#pragma mark - Helper Methods

/**
 * 发送事件到JavaScript
 */
- (void)invokeJSMethod:(NSString *)eventName params:(NSDictionary *)params {
    NSDictionary *messageData = @{
        @"event": eventName,
        @"payload": params
    };
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"AbcASRCallback" messageBody:messageData];
}

/**
 * 模块销毁时清理资源
 */
- (void)cleanup {
    NSLog(@"[AbcAsrModule] Cleaning up AbcAsrModule resources");

    // 清理直接模式管理器
    if (self.directAsrManager) {
        [self.directAsrManager cleanup];
        self.directAsrManager = nil;
    }

    // 清理后台服务处理器
    if (self.backgroundHandler) {
        [self.backgroundHandler stopService];
        self.backgroundHandler = nil;
    }

    // 清理所有活跃会话
    [self.activeSessions removeAllObjects];

    // 重置状态
    self.currentConfig = nil;

    NSLog(@"[AbcAsrModule] AbcAsrModule cleanup completed");
}

- (void)dealloc {
    [self cleanup];
    NSLog(@"[AbcAsrModule] AbcAsrModule deallocated");
}

@end
