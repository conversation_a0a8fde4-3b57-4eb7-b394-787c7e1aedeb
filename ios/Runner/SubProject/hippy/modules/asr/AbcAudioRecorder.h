//
//  AbcAudioRecorder.h
//  AbcASR
//
//  iOS音频录制器，对应Android的AbcAudioRecorder
//

#import <Foundation/Foundation.h>
#import <AVFoundation/AVFoundation.h>
#import "AbcAsrConfig.h"

NS_ASSUME_NONNULL_BEGIN

@protocol AbcAudioRecorderDelegate <NSObject>

@optional
- (void)audioRecorder:(id)recorder didReceiveAudioData:(NSData *)audioData;
- (void)audioRecorder:(id)recorder didReceiveWaveformData:(NSArray<NSNumber *> *)waveformData;
- (void)audioRecorderDidStartRecording:(id)recorder;
- (void)audioRecorderDidStopRecording:(id)recorder;
- (void)audioRecorder:(id)recorder didFailWithError:(NSError *)error;

@end

@interface AbcAudioRecorder : NSObject

@property (nonatomic, weak, nullable) id<AbcAudioRecorderDelegate> delegate;
@property (nonatomic, strong, readonly) AbcAsrConfig *config;
@property (nonatomic, assign, readonly) BOOL isRecording;

// 初始化方法
- (instancetype)initWithConfig:(AbcAsrConfig *)config;

// 录音控制
- (BOOL)startRecording;
- (void)stopRecording;

@end

NS_ASSUME_NONNULL_END
