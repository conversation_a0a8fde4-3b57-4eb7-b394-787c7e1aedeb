//
//  AbcAsrWebSocketClient.m
//  AbcASR
//
//  iOS Socket.IO客户端实现，对应Android的AbcAsrWebSocketClient
//  基于现有的SocketIOModule实现
//

#import "AbcAsrWebSocketClient.h"
#import <SocketIO-Swift.h>

@interface AbcAsrWebSocketClient ()

@property (nonatomic, strong) SocketManager *socketManager;
@property (nonatomic, strong) SocketIOClient *socket;
@property (nonatomic, strong) NSMutableDictionary<NSString *, void(^)(NSArray *)> *eventCallbacks;

@end

@implementation AbcAsrWebSocketClient

- (instancetype)initWithConfig:(AbcAsrConfig *)config {
    self = [super init];
    if (self) {
        _config = config;
        _eventCallbacks = [[NSMutableDictionary alloc] init];
        [self initializeSocketIO];
    }
    return self;
}

- (BOOL)isConnected {
    return self.socket && self.socket.status == SocketIOStatusConnected;
}

- (void)initializeSocketIO {
    NSLog(@"[AbcAsrWebSocketClient] Initializing SocketIO with config: %@", self.config);
    NSMutableDictionary *configDict = [[NSMutableDictionary alloc] init];
    
    // 配置传输方式
    if (self.config.forceWebsockets) {
        [configDict setObject:@YES forKey:@"forceWebsockets"];
        NSLog(@"[AbcAsrWebSocketClient] Force WebSocket transport enabled");
    }
    
    // 配置自动重连
    [configDict setObject:@YES forKey:@"reconnects"];
    if (self.config.reconnectionDelay > 0) {
        [configDict setObject:@(self.config.reconnectionDelay) forKey:@"reconnectWait"];
        NSLog(@"[AbcAsrWebSocketClient] Reconnection delay set to: %ldms", (long)self.config.reconnectionDelay);
    }
    
    // 配置Socket.IO路径
    if (self.config.socketPath && [self.config.socketPath isKindOfClass:[NSString class]] && self.config.socketPath.length > 0) {
        [configDict setObject:self.config.socketPath forKey:@"path"];
        NSLog(@"[AbcAsrWebSocketClient] Socket.IO path set to: %@", self.config.socketPath);
    } else if (self.config.socketPath && ![self.config.socketPath isKindOfClass:[NSString class]]) {
        NSLog(@"[AbcAsrWebSocketClient] ❌ socketPath is not a string: %@ (class: %@)", self.config.socketPath, [self.config.socketPath class]);
    }
    
    // 配置查询参数
    if (self.config.connectParams && self.config.connectParams.count > 0) {
        NSMutableDictionary *connectParams = [[NSMutableDictionary alloc] init];
        for (NSString *key in self.config.connectParams) {
            [connectParams setObject:self.config.connectParams[key] forKey:key];
        }
        [configDict setObject:connectParams forKey:@"connectParams"];
        NSLog(@"[AbcAsrWebSocketClient] Connect params added: %@", connectParams);
    }
    
    // 配置额外请求头
    NSMutableDictionary *extraHeaders = [[NSMutableDictionary alloc] init];
    
    // 处理Cookie
    if (self.config.cookies && self.config.cookies.length > 0) {
        NSDictionary *headerFields = [NSDictionary dictionaryWithObject:self.config.cookies forKey:@"Set-Cookie"];
        NSArray *cookies = [NSHTTPCookie cookiesWithResponseHeaderFields:headerFields
                                                                   forURL:[NSURL URLWithString:self.config.serverUrl]];
        [configDict setObject:cookies forKey:@"cookies"];
        
    }

    
    // 添加用户代理
    if (self.config.userAgent) {
        [extraHeaders setObject:self.config.userAgent forKey:@"User-Agent"];
    }
    
    // 添加自定义请求头
    if (self.config.extraHeaders && self.config.extraHeaders.count > 0) {
        for (NSString *key in self.config.extraHeaders) {
            [extraHeaders setObject:self.config.extraHeaders[key] forKey:key];
        }
    }
    
    // 添加默认的客户端标识头
    [extraHeaders setObject:@"iOS" forKey:@"X-Client-Type"];
    [extraHeaders setObject:@"2.0" forKey:@"X-ASR-Version"];
    
    if (extraHeaders.count > 0) {
        [configDict setObject:extraHeaders forKey:@"extraHeaders"];
    }
    
    // 强制创建新连接，避免和其他SocketIO模块冲突
    [configDict setObject:@YES forKey:@"forceNew"];
    
    [configDict setObject:@YES forKey:@"log"];
    
    NSLog(@"[AbcAsrWebSocketClient] Initializing Socket.IO with URL: %@", self.config.serverUrl);
    NSLog(@"[AbcAsrWebSocketClient] Socket.IO config: %@", configDict);
    
    // 创建SocketManager
    NSURL *serverURL = [NSURL URLWithString:self.config.serverUrl];
    self.socketManager = [[SocketManager alloc] initWithSocketURL:serverURL config:configDict];
    
    // 创建Socket客户端
    if (self.config.namespace && [self.config.namespace isKindOfClass:[NSString class]] && self.config.namespace.length > 0) {
        // 确保namespace以/开头
        NSString *namespace = self.config.namespace;
        if (![namespace hasPrefix:@"/"]) {
            namespace = [NSString stringWithFormat:@"/%@", namespace];
            NSLog(@"[AbcAsrWebSocketClient] Fixed namespace to have leading /: %@", namespace);
        }
        self.socket = [self.socketManager socketForNamespace:namespace];
        NSLog(@"[AbcAsrWebSocketClient] Using namespace: %@", namespace);
    } else {
        self.socket = self.socketManager.defaultSocket;
        if (self.config.namespace && ![self.config.namespace isKindOfClass:[NSString class]]) {
            NSLog(@"[AbcAsrWebSocketClient] ❌ namespace is not a string: %@ (class: %@)", self.config.namespace, [self.config.namespace class]);
        }
    }
    
    [self setupSocketEventHandlers];
    
    NSLog(@"[AbcAsrWebSocketClient] Socket.IO initialization completed successfully");
}

- (void)setupSocketEventHandlers {
    __weak typeof(self) weakSelf = self;
    
    // 连接事件
    [self.socket on:@"connect" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        NSLog(@"[AbcAsrWebSocketClient] 🔌 Connected to Socket.IO server, sessionId: %@", weakSelf.socket.sid);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([weakSelf.delegate respondsToSelector:@selector(webSocketClientDidConnect:)]) {
                [weakSelf.delegate webSocketClientDidConnect:weakSelf];
            }
        });
    }];
    
    // 连接错误事件
    [self.socket on:@"connect_error" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Failed to connect to Socket.IO server: %@", data);
        
        // 提取详细错误信息
        NSString *errorMessage = @"Failed to connect to server";
        if (data.count > 0) {
            id errorData = data[0];
            if ([errorData isKindOfClass:[NSError class]]) {
                NSError *socketError = (NSError *)errorData;
                errorMessage = [NSString stringWithFormat:@"Connection failed: %@ (Code: %ld)", socketError.localizedDescription, (long)socketError.code];
            } else if ([errorData isKindOfClass:[NSString class]]) {
                errorMessage = [NSString stringWithFormat:@"Connection failed: %@", errorData];
            } else {
                errorMessage = [NSString stringWithFormat:@"Connection failed: %@", [errorData description]];
            }
        }
        
        NSError *error = [NSError errorWithDomain:@"AbcAsrWebSocketClient" 
                                             code:-1001 
                                         userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([weakSelf.delegate respondsToSelector:@selector(webSocketClient:didFailWithError:)]) {
                [weakSelf.delegate webSocketClient:weakSelf didFailWithError:error];
            }
        });
    }];
    
    // 断开连接事件
    [self.socket on:@"disconnect" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        NSLog(@"[AbcAsrWebSocketClient] 🔌 Disconnected from Socket.IO server: %@", data);
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([weakSelf.delegate respondsToSelector:@selector(webSocketClient:didDisconnectWithError:)]) {
                [weakSelf.delegate webSocketClient:weakSelf didDisconnectWithError:nil];
            }
        });
    }];
    
    // 通用错误事件
    [self.socket on:@"error" callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Socket.IO error: %@", data);
        
        // 提取详细错误信息
        NSString *errorMessage = @"Socket.IO error";
        if (data.count > 0) {
            id errorData = data[0];
            if ([errorData isKindOfClass:[NSError class]]) {
                NSError *socketError = (NSError *)errorData;
                errorMessage = [NSString stringWithFormat:@"Socket.IO error: %@ (Code: %ld)", socketError.localizedDescription, (long)socketError.code];
            } else if ([errorData isKindOfClass:[NSString class]]) {
                errorMessage = [NSString stringWithFormat:@"Socket.IO error: %@", errorData];
            } else {
                errorMessage = [NSString stringWithFormat:@"Socket.IO error: %@", [errorData description]];
            }
        }
        
        NSError *error = [NSError errorWithDomain:@"AbcAsrWebSocketClient" 
                                             code:-1002 
                                         userInfo:@{NSLocalizedDescriptionKey: errorMessage}];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([weakSelf.delegate respondsToSelector:@selector(webSocketClient:didFailWithError:)]) {
                [weakSelf.delegate webSocketClient:weakSelf didFailWithError:error];
            }
        });
    }];
}

- (BOOL)connect {
    NSLog(@"[AbcAsrWebSocketClient] 🚀 Starting Socket.IO connection process...");
    NSLog(@"[AbcAsrWebSocketClient] Server URL: %@", self.config.serverUrl);
    NSLog(@"[AbcAsrWebSocketClient] Socket path: %@", self.config.socketPath);
    NSLog(@"[AbcAsrWebSocketClient] Namespace: %@", self.config.namespace);
    NSLog(@"[AbcAsrWebSocketClient] Force WebSockets: %@", self.config.forceWebsockets ? @"YES" : @"NO");
    
    if (self.socket) {
        NSLog(@"[AbcAsrWebSocketClient] 🔌 Calling socket.connect()...");
        [self.socket connect];
        return YES;
    } else {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Socket is nil, cannot connect");
        return NO;
    }
}

- (void)disconnect {
    NSLog(@"[AbcAsrWebSocketClient] Disconnecting Socket.IO");
    
    if (self.socket) {
        [self.socket disconnect];
    }
}

- (void)sendMessage:(NSDictionary *)data forEvent:(NSString *)event {
    if (self.socket && self.socket.status == SocketIOStatusConnected) {
        NSLog(@"[AbcAsrWebSocketClient] 📤 Emitting event '%@' with data: %@", event, data);
        [self.socket emit:event with:@[data]];
    } else {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Cannot send message - socket not connected");
    }
}

- (void)sendAudioData:(NSData *)audioData forEvent:(NSString *)event {
    if (self.socket && self.socket.status == SocketIOStatusConnected) {
        NSLog(@"[AbcAsrWebSocketClient] 📤 Emitting event '%@' with audio data (%lu bytes)", event, (unsigned long)audioData.length);
        [self.socket emit:event with:@[audioData]];
    } else {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Cannot send audio data - socket not connected");
    }
}

- (void)addEventListener:(NSString *)event callback:(void(^)(NSArray *items))callback {
    if (!event || event.length == 0) {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Cannot add listener for empty event");
        return;
    }
    
    // 存储回调
    if (callback) {
        [self.eventCallbacks setObject:callback forKey:event];
    }
    
    __weak typeof(self) weakSelf = self;
    [self.socket on:event callback:^(NSArray * _Nonnull data, SocketAckEmitter * _Nonnull ack) {
        NSLog(@"[AbcAsrWebSocketClient] 📥 Received event '%@' with data: %@", event, data);
        //忽略ping/pong事件
        if ([event isEqualToString:@"ping"]|| [event isEqualToString:@"pong"]) return;
        
        // 调用存储的回调
        void(^storedCallback)(NSArray *) = weakSelf.eventCallbacks[event];
        if (storedCallback) {
            storedCallback(data);
        }
        
        // 通知代理
        dispatch_async(dispatch_get_main_queue(), ^{
            if ([weakSelf.delegate respondsToSelector:@selector(webSocketClient:didReceiveMessage:forEvent:)]) {
                [weakSelf.delegate webSocketClient:weakSelf didReceiveMessage:data forEvent:event];
            }
        });
    }];
    
    NSLog(@"[AbcAsrWebSocketClient] ✅ Added event listener for: %@", event);
}

- (void)removeEventListener:(NSString *)event {
    if (!event || event.length == 0) {
        NSLog(@"[AbcAsrWebSocketClient] ❌ Cannot remove listener for empty event");
        return;
    }
    
    [self.socket off:event];
    [self.eventCallbacks removeObjectForKey:event];
    
    NSLog(@"[AbcAsrWebSocketClient] ✅ Removed event listener for: %@", event);
}

- (void)cleanup {
    NSLog(@"[AbcAsrWebSocketClient] Cleaning up WebSocket client resources");
    
    [self disconnect];
    
    if (self.socket) {
        [self.socket removeAllHandlers];
        self.socket = nil;
    }
    
    self.socketManager = nil;
    [self.eventCallbacks removeAllObjects];
    
    NSLog(@"[AbcAsrWebSocketClient] WebSocket client resources cleaned up");
}

- (void)dealloc {
    [self cleanup];
}

@end
