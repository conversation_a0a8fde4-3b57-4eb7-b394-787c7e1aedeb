//
//  AbcAsrWebSocketClient.h
//  AbcASR
//
//  iOS Socket.IO客户端，对应Android的AbcAsrWebSocketClient
//  基于现有的SocketIOModule实现
//

#import <Foundation/Foundation.h>
#import "AbcAsrConfig.h"

NS_ASSUME_NONNULL_BEGIN

@protocol AbcAsrWebSocketClientDelegate <NSObject>

@optional
- (void)webSocketClientDidConnect:(id)client;
- (void)webSocketClient:(id)client didDisconnectWithError:(nullable NSError *)error;
- (void)webSocketClient:(id)client didReceiveMessage:(NSArray *)items forEvent:(NSString *)event;
- (void)webSocketClient:(id)client didFailWithError:(NSError *)error;

@end

@interface AbcAsrWebSocketClient : NSObject

@property (nonatomic, weak, nullable) id<AbcAsrWebSocketClientDelegate> delegate;
@property (nonatomic, strong, readonly) AbcAsrConfig *config;
@property (nonatomic, assign, readonly) BOOL isConnected;

// 初始化方法
- (instancetype)initWithConfig:(AbcAsrConfig *)config;

// 连接控制
- (BOOL)connect;
- (void)disconnect;

// 消息发送
- (void)sendMessage:(NSDictionary *)data forEvent:(NSString *)event;
- (void)sendAudioData:(NSData *)audioData forEvent:(NSString *)event;

// 事件监听 (对应Android的on/off方法)
- (void)addEventListener:(NSString *)event callback:(void(^)(NSArray *items))callback;
- (void)removeEventListener:(NSString *)event;

// 清理资源
- (void)cleanup;

@end

NS_ASSUME_NONNULL_END