//
//  PermissionManager.h
//  permission_handler
//
//  Created by <PERSON><PERSON><PERSON> on 15/02/2019.
//
#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

#import "AudioVideoPermissionStrategy.h"
#import "ContactPermissionStrategy.h"
#import "EventPermissionStrategy.h"
#import "LocationPermissionStrategy.h"
#import "MediaLibraryPermissionStrategy.h"
#import "PermissionStrategy.h"
#import "PhonePermissionStrategy.h"
#import "PhotoPermissionStrategy.h"
#import "SensorPermissionStrategy.h"
#import "SpeechPermissionStrategy.h"
#import "StoragePermissionStrategy.h"
#import "UnknownPermissionStrategy.h"
#import "NotificationPermissionStrategy.h"
#import "PermissionHandlerEnums.h"
#import "Codec.h"

#import "ABCMacros.h"
#import "HippyMethodInvoideCallback.h"
#import "HippyBridgeModule.h"


typedef void (^PermissionRequestCompletion)(NSDictionary *permissionRequestResults);

@interface PermissionManager : NSObject

- (instancetype)initWithStrategyInstances;
- (void)requestPermissions:(NSArray *)permissions completion:(PermissionRequestCompletion)completion;

+ (void)checkPermissionStatus:(enum PermissionGroup)permission result:(HippyMethodInvoideCallback)result;
+ (void)checkServiceStatus:(enum PermissionGroup)permission result:(HippyMethodInvoideCallback)result;
+ (void)openAppSettings:(HippyMethodInvoideCallback)result;

@end
