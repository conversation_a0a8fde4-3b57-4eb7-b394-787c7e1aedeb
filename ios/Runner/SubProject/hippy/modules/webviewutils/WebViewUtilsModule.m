#import "WebViewUtilsModule.h"
#import <Photos/Photos.h>
#import <UIKit/UIKit.h>
#import <WebKit/WebKit.h>
#import "ABCMacros.h"
#import "HippyMethodInvoideCallback.h"

@interface WKWebViewHolder : NSObject
@property (nonatomic, retain) WKWebView* webview;
@end

@implementation WKWebViewHolder

-(instancetype) init{
    self = [super init];
    return self;
}
@end

@implementation WebViewUtilsModule
HIPPY_EXPORT_MODULE(WebViewUtils)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_METHOD(clearCookies:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self clearCookies:GeneralHippyInvokeCallbackHandler];
}

HIPPY_EXPORT_METHOD(getDefaultUserAgent:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self getDefaultUserAgent:GeneralHippyInvokeCallbackHandler];
}

HIPPY_EXPORT_METHOD(setCookie:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self setCookie:params result:GeneralHippyInvokeCallbackHandler];
}


HIPPY_EXPORT_METHOD(getCookie:(NSDictionary*)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    [self getCookie:[params objectForKey:@"url"] result:GeneralHippyInvokeCallbackHandler];
}


- (void)clearCookies:(HippyMethodInvoideCallback)result {
    if (@available(iOS 9.0, *)) {
        NSSet<NSString *> *websiteDataTypes = [NSSet setWithObject:WKWebsiteDataTypeCookies];
        WKWebsiteDataStore *dataStore = [WKWebsiteDataStore defaultDataStore];
        
        void (^deleteAndNotify)(NSArray<WKWebsiteDataRecord *> *) =
        ^(NSArray<WKWebsiteDataRecord *> *cookies) {
            BOOL hasCookies = cookies.count > 0;
            [dataStore removeDataOfTypes:websiteDataTypes
                          forDataRecords:cookies
                       completionHandler:^{
                result(@(hasCookies), nil);
            }];
        };
        
        [dataStore fetchDataRecordsOfTypes:websiteDataTypes completionHandler:deleteAndNotify];
    } else {
        // support for iOS8 tracked in https://github.com/flutter/flutter/issues/27624.
        Log(@"Clearing cookies is not supported for Flutter WebViews prior to iOS 9.");
    }
}


- (void) getDefaultUserAgent:(HippyMethodInvoideCallback) result {
    WKWebViewHolder* holder = [[WKWebViewHolder alloc]init];
    holder.webview = [[WKWebView alloc] initWithFrame:CGRectZero];
    [holder.webview evaluateJavaScript:@"navigator.userAgent" completionHandler:^(id ret, NSError * _Nullable error) {
        result(ret, nil);
        holder.webview = nil;
    }];
}


- (void)setCookie: (NSDictionary*) arguments result:(HippyMethodInvoideCallback) result{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        NSString* urlString = arguments[@"url"];
        NSString* cookie = arguments[@"cookie"];
        if (urlString.length == 0 || cookie.length == 0) {
            result(@(false), nil);
            return;
        }
        
        NSDictionary *headerFields = [NSDictionary dictionaryWithObject:cookie forKey:@"Set-Cookie"];
        
        NSURL* url = [NSURL URLWithString:urlString];
        // Handle cookies
        NSArray *newCookies = [NSHTTPCookie cookiesWithResponseHeaderFields:headerFields forURL:url];
        
        if ([newCookies count]) {
            [[NSHTTPCookieStorage sharedHTTPCookieStorage] setCookies:newCookies forURL:url mainDocumentURL:nil];
        }
        result(@(true), nil);
    });
}

- (void)getCookie:(NSString *)urlString result:(HippyMethodInvoideCallback) result
{
    @autoreleasepool {
        static NSDateFormatter *dateFormat;
        static dispatch_once_t onceToken;
        
        dispatch_once(&onceToken, ^{
            dateFormat = [[NSDateFormatter alloc] init];
            [dateFormat setFormatterBehavior:NSDateFormatterBehavior10_4];
            [dateFormat setTimeZone:[NSTimeZone timeZoneWithAbbreviation:@"GMT"]];
            [dateFormat setDateFormat:@"EEE, dd MMM y HH:mm:ss 'GMT'"];
            [dateFormat setLocale:[[NSLocale alloc] initWithLocaleIdentifier:@"en_US"]];
        });
        
        NSHTTPCookieStorage *cookieStorage = [NSHTTPCookieStorage sharedHTTPCookieStorage];
        NSURL *url = [NSURL URLWithString:[self stringByConvertToCFURL:urlString]];
        if (url == nil || url.host == nil) {
            result(@"", nil);
            return;
        }
        NSArray *array = [cookieStorage cookiesForURL: url];
        if (array.count > 0) {
            NSMutableArray *resultArray = [[NSMutableArray alloc] init];
            for (NSHTTPCookie *cookie in array) {
                if (!cookie.isHTTPOnly && (cookie.expiresDate == nil || [cookie.expiresDate timeIntervalSinceReferenceDate] > [NSDate timeIntervalSinceReferenceDate])) {
                    NSMutableString *str = [NSMutableString string];
                    [str appendFormat:@"%@=%@", [cookie name], [cookie value]];
                    [resultArray addObject:str];
                }
            }
            if (resultArray.count > 0)
            {
                NSString* ret = [resultArray componentsJoinedByString:@"; "];
                result(ret, nil);
                return;
            }
        }
        
        result(@"", nil);
    }
}

- (NSString *)stringByConvertToCFURL:(NSString*) inURLString
{
    if ([inURLString length] > 0) {
        NSData *urlData = [inURLString dataUsingEncoding:NSUTF8StringEncoding];
        NSUInteger urlDataLen = [urlData length];
        UInt8 *bytes = (UInt8 *)[urlData bytes];
        
        CFURLRef urlRef = CFURLCreateWithBytes(NULL, bytes, urlDataLen, kCFStringEncodingUTF8, NULL);
        
        if (NULL == urlRef)
        {
            return inURLString;
        }
        
        NSString *returnUrl = (NSString *)CFURLGetString(urlRef);
        
        CFRelease(urlRef);
        if ([returnUrl isKindOfClass:[NSNull class]]) {
            return inURLString;
        }
        return returnUrl;
    }
    
    return nil;
    
}
@end
