//
//  SocketIOModule.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "SocketIOModule.h"
#import "Log.h"
#import "UIUtils.h"
#import "ABCMacros.h"
#import "HostHippyMessageBridge.h"

#import <SocketIO-Swift.h>




@interface SocketIOSession:NSObject
@property(nonatomic, strong) NSString* sessionId;
@property(nonatomic, strong) SocketManager* socketManager;
@property(nonatomic, strong) NSString* namespace;

- (instancetype)initWithSessionId:(NSString*) sessionId manager:(SocketManager*) manager namespace:(NSString*) namespace;
@end


@implementation SocketIOSession
- (instancetype)initWithSessionId:(NSString*) sessionId manager:(SocketManager*) manager namespace:(NSString*) namespace
{
    self = [super init];
    if (self) {
        _sessionId = sessionId;
        _socketManager = manager;
        _namespace = namespace;
    }
    return self;
}
@end

@implementation SocketIOModule {
    NSMutableDictionary<NSString*,SocketIOSession*>* _sockets;
}
HIPPY_EXPORT_MODULE(SocketIO)

- (instancetype)init
{
    self = [super init];
    if (self) {
        _sockets = [NSMutableDictionary dictionary];
    }
    
    return self;
}
- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

-(SocketIOClient*) socketWithSessionId:(NSString*) sessionId {
    SocketIOSession* session = [_sockets objectForKey:sessionId];
    return [session.socketManager socketForNamespace:session.namespace];
}

HIPPY_EXPORT_METHOD(create:(nonnull NSString*)sessionId url:(nonnull NSString*) url
                    params:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    dispatch_async([self methodQueue], ^{
        const NSString* kCookie = @"cookies";
        const NSString* kNamespace = @"namespace";
        NSMutableDictionary* finalParams = [NSMutableDictionary dictionaryWithDictionary:params?:@{}];
        NSArray* rawCookies = [finalParams objectForKey:kCookie];
        NSString* namespace = [finalParams objectForKey:kNamespace];
        [finalParams removeObjectForKey:kNamespace];

        //解析cookie
        if (rawCookies != nil) {
            NSDictionary *headerFields = [NSDictionary dictionaryWithObject:rawCookies forKey:@"Set-Cookie"];
            NSArray *newCookies = [NSHTTPCookie cookiesWithResponseHeaderFields:headerFields forURL:[NSURL URLWithString:url]];
            [finalParams setObject:newCookies forKey:kCookie];
        }
        

        SocketManager* manager = [[SocketManager alloc] initWithSocketURL:[NSURL URLWithString:url]
                                                       config:finalParams];
        SocketIOClient* socket;
        if (namespace)
             socket = [manager socketForNamespace:namespace];
        else
            socket = manager.defaultSocket;
        
        
        [_sockets setObject:[[SocketIOSession alloc] initWithSessionId:sessionId manager:manager namespace:namespace] forKey:sessionId];
        
        [socket onAny:^(SocketAnyEvent * _Nonnull evt) {
            //忽略ping/pong事件
            if ([evt.event isEqualToString:@"ping"]|| [evt.event isEqualToString:@"pong"]) return;
            
            NSMutableDictionary* body = [NSMutableDictionary dictionaryWithObjectsAndKeys:sessionId, @"sessionId",
                                                               evt.event, @"name",nil];
            if (evt.items)  {
                [body setObject:evt.items forKey:@"items"];
            }
            
            [[HostHippyMessageBridge sharedInstance] onHostMessage:@"socketIOOnEvent" messageBody:body];
        }];
        
        resolve(@(YES));
    });
}


HIPPY_EXPORT_METHOD(connect:(nonnull NSString*)sessionId resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    dispatch_async([self methodQueue], ^{
        if (!sessionId.length) {
            reject(@"-1", @"参数sessionId空",nil);
            return;
        }
        
        SocketIOClient* socket = [self socketWithSessionId:sessionId];
        if (socket == nil) {
            reject(@"-1", @"调用connect之前请先create创建",nil);
            return;
        }
        [socket connect];
        resolve(@(YES));
    });
}

HIPPY_EXPORT_METHOD(disconnect:(nonnull NSString*)sessionId resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    dispatch_async([self methodQueue], ^{
        if (!sessionId.length) {
            reject(@"-1", @"参数sessionId空",nil);
            return;
        }
        
        SocketIOClient* socket = [self socketWithSessionId:sessionId];
        if (socket == nil) {
            reject(@"-1", @"调用connect之前请先create创建",nil);
            return;
        }
        
        [socket disconnect];
        [_sockets removeObjectForKey:sessionId];
        resolve(@(YES));
    });
}

HIPPY_EXPORT_METHOD(emit:(nonnull NSString*)sessionId name:(NSString*) name body: (NSDictionary*) body resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    dispatch_async([self methodQueue], ^{
        if (!sessionId.length) {
            reject(@"-1", @"参数sessionId空",nil);
            return;
        }
        
        SocketIOClient* socket = [self socketWithSessionId:sessionId];
        if (socket == nil) {
            reject(@"-1", @"调用connect之前请先create创建",nil);
            return;
        }
        
        [socket emit:name with:[NSArray arrayWithObjects:body, nil]];
        resolve(@(YES));
    });
}


@end
