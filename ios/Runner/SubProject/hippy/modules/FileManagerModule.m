//
//  FileManagerModule.m
//  Runner
//
//  Created by f<PERSON><PERSON> on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import "FileManagerModule.h"
#import "Log.h"
#import "UIUtils.h"
#import "ABCMacros.h"
#import "HostHippyMessageBridge.h"

@implementation FileManagerModule
HIPPY_EXPORT_MODULE(FileManager)

- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}

HIPPY_EXPORT_METHOD(exists:(nonnull NSString *)file resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:file];
    resolve(@(fileExists));
}


HIPPY_EXPORT_METHOD(writeAsString:(nonnull NSString *)file content:(NSString*) content encode:(NSString*)encode resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSLog(@"FileManagerModule writeAsString path = %@, encode = %@", file, encode);
    NSInteger encodeType = NSUTF8StringEncoding;
     if ([encode isEqualToString:@"utf8"]) {
         encodeType = NSUTF8StringEncoding;
     }
    NSError* error = nil;

    [content writeToFile:file atomically:YES encoding:encodeType error:nil];
    if (error != nil) {
        reject(@"FileError",@"文件写入出错", error);
        return;
    }

    resolve(@(YES));
}

HIPPY_EXPORT_METHOD(readAsString:(nonnull NSString *)file encode:(NSString*)encode resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSLog(@"FileManagerModule readAsString path = %@, encode = %@", file, encode);
    BOOL fileExists = [[NSFileManager defaultManager] fileExistsAtPath:file];

    if (!fileExists) {
        reject(@"FileError",[NSString stringWithFormat:@"文件%@不存在", file], nil);
        return;
    }

    NSError* error = nil;

    // 当encoding为base64时，需要读取文件的字节数据并编码为base64字符串
    if ([encode isEqualToString:@"base64"]) {
        NSData *fileData = [NSData dataWithContentsOfFile:file options:0 error:&error];
        if (error != nil) {
            reject(@"FileError", @"文件读取出错", error);
            return;
        }

        NSString *base64String = [fileData base64EncodedStringWithOptions:0];
        resolve(base64String);
        return;
    }

    NSInteger encodeType = NSUTF8StringEncoding;
    if ([encode isEqualToString:@"utf8"]) {
        encodeType = NSUTF8StringEncoding;
    }

    NSString *content = [NSString stringWithContentsOfFile:file encoding:encodeType error:&error];
    if (error != nil) {
        reject(@"FileError",@"文件读取出错", error);
        return;
    }

    resolve(content);
}


HIPPY_EXPORT_METHOD(mkdirs:(nonnull NSString *)dirs resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSLog(@"FileManagerModule mkdirs path =%@", dirs);
    NSError* error= nil;
   [[NSFileManager defaultManager] createDirectoryAtPath:dirs
       withIntermediateDirectories:YES
                        attributes:nil
                             error:&error];
    if (error != nil) {
        reject(@"FileError",@"创建失败", error);
        return;
    }
    resolve(@(YES));
}

HIPPY_EXPORT_METHOD(delete:(nonnull NSString *)file resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSLog(@"FileManagerModule delete path =%@", file);
    NSError* error= nil;
    [[NSFileManager defaultManager] removeItemAtPath:file error:&error];
    if (error != nil) {
        reject(@"FileError",@"删除失败", error);
        return;
    }
    resolve(@(YES));
}




HIPPY_EXPORT_METHOD(mv:(nonnull NSString *)from to:(nonnull NSString*) to resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSLog(@"FileManagerModule mv from =%@, to = %@", from, to);
    NSError* error= nil;
    [[NSFileManager defaultManager] moveItemAtPath:from toPath:to error:&error];
    if (error != nil) {
        reject(@"FileError",@"移动失败", error);
        return;
    }
    resolve(@(YES));
}



HIPPY_EXPORT_METHOD(getApplicationDocumentsDirectory:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    resolve([FileManagerModule getPathForDirectory:NSDocumentDirectory]);
}

HIPPY_EXPORT_METHOD(getTemporaryDirectory:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    resolve(NSTemporaryDirectory());
}

+ (NSString *)getPathForDirectory:(int)directory
{
  NSArray *paths = NSSearchPathForDirectoriesInDomains(directory, NSUserDomainMask, YES);
  return [paths firstObject];
}

@end
