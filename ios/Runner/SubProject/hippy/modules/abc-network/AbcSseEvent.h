/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * SSE 事件数据结构
 * 对应 Android 端的 SseEvent.java
 */
@interface AbcSseEvent : NSObject

@property (nonatomic, strong, nullable) NSString *type;
@property (nonatomic, strong, nullable) NSString *data;
@property (nonatomic, strong, nullable) NSString *eventId;
@property (nonatomic, assign) NSInteger retry;

- (instancetype)initWithType:(nullable NSString *)type 
                        data:(nullable NSString *)data 
                     eventId:(nullable NSString *)eventId 
                       retry:(NSInteger)retry;

- (BOOL)hasData;
- (NSDictionary *)toDictionary;

@end

/**
 * SSE 事件构建器
 * 对应 Android 端的 SseEvent.Builder
 */
@interface AbcSseEventBuilder : NSObject

@property (nonatomic, strong, nullable) NSString *type;
@property (nonatomic, strong, nullable) NSMutableString *data;
@property (nonatomic, strong, nullable) NSString *eventId;
@property (nonatomic, assign) NSInteger retry;

- (void)appendData:(NSString *)data;
- (void)setType:(NSString *)type;
- (void)setEventId:(NSString *)eventId;
- (void)setRetry:(NSInteger)retry;
- (AbcSseEvent *)build;
- (void)reset;

@end

NS_ASSUME_NONNULL_END
