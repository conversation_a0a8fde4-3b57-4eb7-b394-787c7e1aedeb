/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "AbcSseEvent.h"

@implementation AbcSseEvent

- (instancetype)initWithType:(nullable NSString *)type 
                        data:(nullable NSString *)data 
                     eventId:(nullable NSString *)eventId 
                       retry:(NSInteger)retry {
    self = [super init];
    if (self) {
        _type = type ?: @"message";
        _data = data;
        _eventId = eventId;
        _retry = retry;
    }
    return self;
}

- (BOOL)hasData {
    return self.data != nil && self.data.length > 0;
}

- (NSDictionary *)toDictionary {
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    dict[@"type"] = self.type ?: @"message";
    if (self.data) {
        dict[@"data"] = self.data;
    }
    if (self.eventId) {
        dict[@"id"] = self.eventId;
    }
    if (self.retry > 0) {
        dict[@"retry"] = @(self.retry);
    }
    return [dict copy];
}

- (NSString *)description {
    return [NSString stringWithFormat:@"AbcSseEvent{type='%@', data='%@', id='%@', retry=%ld}", 
            self.type, self.data, self.eventId, (long)self.retry];
}

@end

@implementation AbcSseEventBuilder

- (instancetype)init {
    self = [super init];
    if (self) {
        _type = @"message";
        _data = [NSMutableString string];
        _retry = 0;
    }
    return self;
}

- (void)appendData:(NSString *)data {
    if (data) {
        if (self.data.length > 0) {
            [self.data appendString:@"\n"];
        }
        [self.data appendString:data];
    }
}

- (void)setType:(NSString *)type {
    _type = type ?: @"message";
}

- (void)setEventId:(NSString *)eventId {
    _eventId = eventId;
}

- (void)setRetry:(NSInteger)retry {
    _retry = retry;
}

- (AbcSseEvent *)build {
    return [[AbcSseEvent alloc] initWithType:self.type 
                                        data:self.data.length > 0 ? [self.data copy] : nil
                                     eventId:self.eventId 
                                       retry:self.retry];
}

- (void)reset {
    _type = @"message";
    _data = [NSMutableString string];
    _eventId = nil;
    _retry = 0;
}

@end
