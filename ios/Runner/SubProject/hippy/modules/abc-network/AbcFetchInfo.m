/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "AbcFetchInfo.h"

@implementation AbcFetchInfo

- (instancetype) initWithResolveBlock:(HippyPromiseResolveBlock)resolveBlock rejectBlock:(HippyPromiseRejectBlock)rejectBlock report302Status:(BOOL)report302Status taskId:(NSString*) taskId file:(NSString*) file{
    self = [super init];
    if (self) {
        _resolveBlock = resolveBlock;
        _rejectBlock = rejectBlock;
        _report302Status = report302Status;
        _taskId = taskId;
        _file = file;
        
        _totalLengthBytes = -1;
        _fileOutputStream = [NSOutputStream outputStreamToFileAtPath:_file append:NO];
        [_fileOutputStream open];
    }
    return self;
}

- (void)dealloc
{
    [_fileOutputStream close];
}

@end
