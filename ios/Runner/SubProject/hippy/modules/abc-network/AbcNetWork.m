/*!
* iOS SDK
*
* <PERSON><PERSON> is pleased to support the open source community by making
* Hippy available.
*
* Copyright (C) 2019 THL A29 Limited, a Tencent company.
* All rights reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#import "AbcNetwork.h"
#import "HippyAssert.h"
#import "HippyLog.h"
#import <WebKit/WKHTTPCookieStore.h>
#import <WebKit/WKWebsiteDataStore.h>
#import "HippyUtils.h"
#import "AbcFetchInfo.h"
#import "objc/runtime.h"
#import "FileUtils.h"
#import "HostHippyMessageBridge.h"
#import "AbcSseConnection.h"
#import "AbcSseEvent.h"

static char fetchInfoKey;

static void setFetchInfoForSessionTask(NSURLSessionTask *task, AbcFetchInfo *fetchInfo) {
    objc_setAssociatedObject(task, &fetchInfoKey, fetchInfo, OBJC_ASSOCIATION_RETAIN_NONATOMIC);
}

static AbcFetchInfo *fetchInfoForSessionTask(NSURLSessionTask *task) {
    AbcFetchInfo *info = objc_getAssociatedObject(task, &fetchInfoKey);
    return info;
}

@interface AbcNetwork ()

@property (nonatomic, strong) NSMutableDictionary<NSString *, AbcSseConnection *> *sseConnections;

@end

@implementation AbcNetwork

- (instancetype)init {
    self = [super init];
    if (self) {
        _sseConnections = [NSMutableDictionary dictionary];
    }
    return self;
}

HIPPY_EXPORT_MODULE(AbcNetwork)

HIPPY_EXPORT_METHOD(downloadFile:(NSDictionary *)params resolver:(__unused HippyPromiseResolveBlock)resolve rejecter:(__unused HippyPromiseRejectBlock)reject)
{
    NSString *method = params[@"method"];
    NSString *url = params[@"url"];
    NSDictionary *header = params[@"headers"];
    NSString *body = params[@"body"];

    NSString *taskId = params[@"taskId"];
    NSString *filePath = params[@"filePath"];

    HippyAssertParam(url);
    HippyAssertParam(method);

	if (![header isKindOfClass: [NSDictionary class]]) {
		header = @{};
	}

    NSURL *requestURL = HippyURLWithString(url, NULL);
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:requestURL];
    [request setHTTPMethod: method];

	NSMutableDictionary *httpHeader = [NSMutableDictionary new];
	[header enumerateKeysAndObjectsUsingBlock:^(id  _Nonnull key, id  _Nonnull obj, __unused BOOL *stop) {
		NSString *value = nil;
		if ([obj isKindOfClass: [NSArray class]]) {
			value = [[(NSArray *)obj valueForKey:@"description"] componentsJoinedByString:@","];
		} else if ([obj isKindOfClass: [NSString class]]) {
			value = obj;
		}

		[httpHeader setValue: value forKey: key];
	}];
    if (httpHeader.count) {
		[request setAllHTTPHeaderFields: httpHeader];
	}
    NSDictionary<NSString *, NSString *> *extraHeaders = [self extraHeaders];
    [extraHeaders enumerateKeysAndObjectsUsingBlock:^(NSString * _Nonnull key, NSString * _Nonnull obj, BOOL * _Nonnull stop) {
        [request addValue:obj forHTTPHeaderField:key];
    }];

    if (body.length) {
        NSData *postData = [body dataUsingEncoding: NSUTF8StringEncoding];
        if (postData) {
            [request setHTTPBody: postData];
        }
    }
    NSString *redirect = params[@"redirect"];
    BOOL report302Status = (nil == redirect || [redirect isEqualToString:@"manual"]);
    AbcFetchInfo *fetchInfo = [[AbcFetchInfo alloc] initWithResolveBlock:resolve rejectBlock:reject report302Status:report302Status taskId:taskId file:filePath];

    NSURLSessionConfiguration *sessionConfiguration = [NSURLSessionConfiguration defaultSessionConfiguration];
    sessionConfiguration.protocolClasses = [self protocolClasses];
    NSURLSession *session = [NSURLSession sessionWithConfiguration:sessionConfiguration delegate:self delegateQueue:nil];
    NSURLSessionTask *task = [session dataTaskWithRequest:request];
    setFetchInfoForSessionTask(task, fetchInfo);
    [task resume];
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task
willPerformHTTPRedirection:(NSHTTPURLResponse *)response
        newRequest:(NSURLRequest *)request
 completionHandler:(void (^)(NSURLRequest * _Nullable))completionHandler {
    AbcFetchInfo *fetchInfo = fetchInfoForSessionTask(task);
    if (fetchInfo.report302Status) {
        HippyPromiseResolveBlock resolver = fetchInfo.resolveBlock;
        if (resolver) {
            NSDictionary *result = @{
                                     @"statusCode": @(response.statusCode),
                                     @"statusLine": @"",
                                     @"respHeaders": response.allHeaderFields ? : @{},
                                     @"respBody": @"",
                                     @"filePath": fetchInfo.file
                                     };

            resolver(result);
        }
        completionHandler(nil);
    }
    else {
        completionHandler(request);
    }
}

- (void)URLSession:(NSURLSession *)session task:(NSURLSessionTask *)task
didCompleteWithError:(nullable NSError *)error {
    BOOL is302Response = ([task.response isKindOfClass:[NSHTTPURLResponse class]] && 302 == [(NSHTTPURLResponse *)task.response statusCode]);
    AbcFetchInfo *fetchInfo = fetchInfoForSessionTask(task);
    //如果是302并且禁止自动跳转，那说明已经将302结果发送给服务器，不需要再次发送
    if (is302Response && fetchInfo.report302Status) {
        return;
    }
    if (error) {
        HippyPromiseRejectBlock rejector = fetchInfo.rejectBlock;
        NSString *code = [NSString stringWithFormat:@"%ld", (long)error.code];
        rejector(code,error.description, error);
    }
    else {
        HippyPromiseResolveBlock resolver = fetchInfo.resolveBlock;
        NSHTTPURLResponse *resp = (NSHTTPURLResponse *) task.response;
        NSDictionary *result = @{
                                 @"statusCode": @(resp.statusCode),
                                 @"statusLine": @"",
                                 @"respHeaders": resp.allHeaderFields ? : @{},
                                 @"filePath": fetchInfo.file
                                 };

        resolver(result);
    }
}

- (void)URLSession:(NSURLSession *)session dataTask:(NSURLSessionDataTask *)dataTask
    didReceiveData:(NSData *)data {
    AbcFetchInfo* fetchInfo = fetchInfoForSessionTask(dataTask);
    NSOutputStream* stream = fetchInfo.fileOutputStream;
    [stream write:data.bytes maxLength:data.length];
    fetchInfo.receivedBytes += data.length;

    if (fetchInfo.totalLengthBytes == -1) {
        NSHTTPURLResponse* response = (NSHTTPURLResponse*)dataTask.response;
        NSNumber* length = [response.allHeaderFields objectForKey:@"Content-Length"];
        if(length != nil) {
            fetchInfo.totalLengthBytes = length.integerValue;
        }
    }


    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"downloadFileCallback"
                                               messageBody:@{@"receivedBytes":@(fetchInfo.receivedBytes),
                                                             @"totalBytes":@(fetchInfo.totalLengthBytes),
                                                             @"taskId": fetchInfo.taskId
                                               }];
}

- (NSArray<Class> *) protocolClasses {
    return [NSArray array];
}

- (NSDictionary<NSString *, NSString *> *)extraHeaders {
    return nil;
}

HIPPY_EXPORT_METHOD(getCookie:(NSString *)urlString resolver:(HippyPromiseResolveBlock)resolve rejecter:(__unused HippyPromiseRejectBlock)reject) {
    NSData *uriData = [urlString dataUsingEncoding:NSUTF8StringEncoding];
    if (nil == uriData) {
        resolve(@"");
        return;
    }
    CFURLRef urlRef = CFURLCreateWithBytes(NULL, [uriData bytes], [uriData length], kCFStringEncodingUTF8, NULL);
    NSURL *source_url = CFBridgingRelease(urlRef);
    NSArray<NSHTTPCookie *>* cookies = [[NSHTTPCookieStorage sharedHTTPCookieStorage] cookiesForURL:source_url];
    NSMutableString *string = [NSMutableString stringWithCapacity:256];
    for (NSHTTPCookie *cookie in cookies) {
        [string appendFormat:@";%@=%@", cookie.name, cookie.value];
    }
    if ([string length] > 0) {
        [string deleteCharactersInRange:NSMakeRange(0, 1)];
    }
    resolve(string);
}

HIPPY_EXPORT_METHOD(setCookie:(NSString *)urlString keyValue:(NSString *)keyValue expireString:(NSString *)expireString) {
    NSData *uriData = [urlString dataUsingEncoding:NSUTF8StringEncoding];
    if (nil == uriData) {
        return;
    }
    CFURLRef urlRef = CFURLCreateWithBytes(NULL, [uriData bytes], [uriData length], kCFStringEncodingUTF8, NULL);
    if (NULL == urlRef) {
        return;
    }
    NSURL *source_url = CFBridgingRelease(urlRef);
    NSArray<NSString *> *keysvalues = [keyValue componentsSeparatedByString:@";"];
    NSMutableArray<NSHTTPCookie *>* cookies = [NSMutableArray arrayWithCapacity:[keysvalues count]];
    NSString *path = [source_url path];
    NSString *domain = [source_url host];
    if (nil == path || nil == domain) {
        return;
    }
    dispatch_async(dispatch_get_main_queue(), ^{
        for (NSString *allValues in keysvalues) {
            @autoreleasepool {
                NSArray<NSString *> *value = [allValues componentsSeparatedByString:@"="];
                NSDictionary *dictionary = @{NSHTTPCookieName: value[0], NSHTTPCookieValue: value[1], NSHTTPCookieExpires: expireString, NSHTTPCookiePath: path, NSHTTPCookieDomain: domain};
                NSHTTPCookie *cookie = [NSHTTPCookie cookieWithProperties:dictionary];
                if (cookie) {
                    [cookies addObject:cookie];
                    //给ios11以上的系统设置WKCookie
                    if (@available(iOS 11.0, *)) {
                        WKWebsiteDataStore *ds = [WKWebsiteDataStore defaultDataStore];
                        [ds.httpCookieStore setCookie:cookie completionHandler:NULL];
                    }
                }
            }
        }
        [[NSHTTPCookieStorage sharedHTTPCookieStorage] setCookies:cookies forURL:source_url mainDocumentURL:nil];
    });
}

#pragma mark - SSE Methods

HIPPY_EXPORT_METHOD(connectSSE:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    if (!params) {
        reject(@"invalid_params", @"invalid request param", nil);
        return;
    }

    NSString *url = params[@"url"];
    NSString *method = params[@"method"] ?: @"GET";
    NSString *connectionId = params[@"connectionId"];
    NSDictionary *headers = params[@"headers"];
    NSString *body = params[@"body"];

    if (!url || url.length == 0 || !connectionId || connectionId.length == 0) {
        reject(@"invalid_params", @"no valid url or connectionId for SSE request", nil);
        return;
    }

    // 检查是否已存在相同的连接
    if (self.sseConnections[connectionId]) {
        reject(@"connection_exists", [NSString stringWithFormat:@"SSE connection with id %@ already exists", connectionId], nil);
        return;
    }

    // 创建请求
    NSURL *requestURL = HippyURLWithString(url, NULL);
    if (!requestURL) {
        reject(@"invalid_url", @"Invalid URL", nil);
        return;
    }

    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:requestURL];
    [request setHTTPMethod:method];

    // 设置请求头
    NSMutableDictionary *httpHeaders = [NSMutableDictionary dictionary];
    
    // 兼容有 _headers 的情况
    NSDictionary *actualHeaders = nil;
    
    if (headers && [headers isKindOfClass:[NSDictionary class]]) {
        id potentialHeaders = headers[@"_headers"];
        
        // 检查是否为字典
        if ([potentialHeaders isKindOfClass:[NSDictionary class]]) {
            actualHeaders = potentialHeaders;
        } else if ([headers count] > 0) {
            actualHeaders = headers;
        }
    }

    // 处理自定义请求头
    if (actualHeaders) {
        [actualHeaders enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
            NSString *value = nil;
            if ([obj isKindOfClass:[NSArray class]]) {
                value = [[(NSArray *)obj valueForKey:@"description"] componentsJoinedByString:@","];
            } else if ([obj isKindOfClass:[NSString class]]) {
                value = obj;
            }

            if (value) {
                [httpHeaders setValue:value forKey:key];
            }
        }];
    }

    // 添加额外的请求头
    NSDictionary<NSString *, NSString *> *extraHeaders = [self extraHeaders];
    [extraHeaders enumerateKeysAndObjectsUsingBlock:^(NSString *key, NSString *obj, BOOL *stop) {
        [httpHeaders setValue:obj forKey:key];
    }];

    if (httpHeaders.count > 0) {
        [request setAllHTTPHeaderFields:httpHeaders];
    }

    // 设置请求体
    if (body && body.length > 0) {
        NSData *postData = [body dataUsingEncoding:NSUTF8StringEncoding];
        if (postData) {
            [request setHTTPBody:postData];
        }
    }

    // 创建 SSE 连接
    AbcSseConnection *sseConnection = [[AbcSseConnection alloc] initWithConnectionId:connectionId
                                                                             request:request
                                                                            delegate:self];

    // 保存连接引用
    self.sseConnections[connectionId] = sseConnection;

    // 保存 Promise 回调以便在连接建立后调用
    objc_setAssociatedObject(sseConnection, "resolve", resolve, OBJC_ASSOCIATION_COPY_NONATOMIC);
    objc_setAssociatedObject(sseConnection, "reject", reject, OBJC_ASSOCIATION_COPY_NONATOMIC);

    // 开始连接
    [sseConnection connect];

    HippyLogInfo(@"SSE connection %@ initiated for URL: %@", connectionId, url);
}

HIPPY_EXPORT_METHOD(disconnectSSE:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    if (!params) {
        reject(@"invalid_params", @"invalid request param", nil);
        return;
    }

    NSString *connectionId = params[@"connectionId"];
    if (!connectionId || connectionId.length == 0) {
        reject(@"invalid_params", @"no connectionId for SSE disconnect", nil);
        return;
    }

    AbcSseConnection *connection = self.sseConnections[connectionId];
    if (!connection) {
        reject(@"connection_not_found", [NSString stringWithFormat:@"SSE connection with id %@ not found", connectionId], nil);
        return;
    }

    // 断开连接
    [connection disconnect];
    [self.sseConnections removeObjectForKey:connectionId];

    resolve(@"SSE connection disconnected");

    HippyLogInfo(@"SSE connection %@ disconnected", connectionId);
}

HIPPY_EXPORT_METHOD(getSseConnections:(NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject) {
    NSMutableArray *connections = [NSMutableArray array];

    [self.sseConnections enumerateKeysAndObjectsUsingBlock:^(NSString *key, AbcSseConnection *connection, BOOL *stop) {
        [connections addObject:[connection connectionInfo]];
    }];

    NSDictionary *result = @{
        @"connections": connections,
        @"totalCount": @(self.sseConnections.count)
    };

    resolve(result);
}

#pragma mark - AbcSseConnectionDelegate

- (void)sseConnectionDidConnect:(NSString *)connectionId response:(NSHTTPURLResponse *)response {
    AbcSseConnection *connection = self.sseConnections[connectionId];
    if (!connection) {
        return;
    }

    // 获取保存的 Promise 回调
    HippyPromiseResolveBlock resolve = objc_getAssociatedObject(connection, "resolve");
    if (resolve) {
        // 构建响应数据，与 Android 端保持一致
        NSMutableDictionary *respHeaders = [NSMutableDictionary dictionary];
        [response.allHeaderFields enumerateKeysAndObjectsUsingBlock:^(id key, id obj, BOOL *stop) {
            if ([obj isKindOfClass:[NSString class]]) {
                respHeaders[key] = @[obj];
            } else {
                respHeaders[key] = @[[obj description]];
            }
        }];

        NSDictionary *result = @{
            @"statusCode": @(response.statusCode),
            @"statusLine": @"OK",
            @"connectionId": connectionId,
            @"respHeaders": respHeaders
        };

        resolve(result);

        // 清除 Promise 回调
        objc_setAssociatedObject(connection, "resolve", nil, OBJC_ASSOCIATION_COPY_NONATOMIC);
        objc_setAssociatedObject(connection, "reject", nil, OBJC_ASSOCIATION_COPY_NONATOMIC);
    }

    // 发送状态变化事件
    NSDictionary *statusEvent = @{
        @"connectionId": connectionId,
        @"status": @"connected"
    };
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"sseStatusChanged" messageBody:statusEvent];

    HippyLogInfo(@"SSE connection %@ established successfully", connectionId);
}

- (void)sseConnection:(NSString *)connectionId didReceiveEvent:(AbcSseEvent *)event {
    // 发送 SSE 事件到 JavaScript 层
    NSMutableDictionary *eventData = [[event toDictionary] mutableCopy];
    eventData[@"connectionId"] = connectionId;

    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"sseEvent" messageBody:eventData];

    HippyLogInfo(@"SSE connection %@ received event: %@ eventData: %@", connectionId, event.type, eventData);
}

- (void)sseConnection:(NSString *)connectionId didFailWithError:(NSError *)error {
    AbcSseConnection *connection = self.sseConnections[connectionId];
    if (!connection) {
        return;
    }

    // 获取保存的 Promise 回调
    HippyPromiseRejectBlock reject = objc_getAssociatedObject(connection, "reject");
    if (reject) {
        reject([NSString stringWithFormat:@"%ld", (long)error.code],
               error.localizedDescription ?: @"SSE connection failed",
               error);

        // 清除 Promise 回调
        objc_setAssociatedObject(connection, "resolve", nil, OBJC_ASSOCIATION_COPY_NONATOMIC);
        objc_setAssociatedObject(connection, "reject", nil, OBJC_ASSOCIATION_COPY_NONATOMIC);
    }

    // 发送状态变化事件
    NSDictionary *statusEvent = @{
        @"connectionId": connectionId,
        @"status": @"error",
        @"error": error.localizedDescription ?: @"Unknown error"
    };
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"sseStatusChanged" messageBody:statusEvent];

    // 从连接池中移除
    [self.sseConnections removeObjectForKey:connectionId];

    HippyLogError(@"SSE connection %@ failed with error: %@", connectionId, error.localizedDescription);
}

- (void)sseConnectionDidDisconnect:(NSString *)connectionId {
    // 发送状态变化事件
    NSDictionary *statusEvent = @{
        @"connectionId": connectionId,
        @"status": @"disconnected"
    };
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"sseStatusChanged" messageBody:statusEvent];

    HippyLogInfo(@"SSE connection %@ disconnected", connectionId);
}

#pragma mark - Cleanup

- (void)dealloc {
    // 断开所有 SSE 连接
    [self.sseConnections enumerateKeysAndObjectsUsingBlock:^(NSString *key, AbcSseConnection *connection, BOOL *stop) {
        [connection disconnect];
    }];
    [self.sseConnections removeAllObjects];

    HippyLogInfo(@"AbcNetwork deallocated, all SSE connections closed");
}

@end
