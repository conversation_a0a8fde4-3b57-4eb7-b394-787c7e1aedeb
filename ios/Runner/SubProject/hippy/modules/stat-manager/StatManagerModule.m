#import "StatManagerModule.h"
#import <UMAnalytics/MobClick.h>
#import <UMCommon/UMCommon.h>
#import <UMCommonLog/UMCommonLogHeaders.h>
#import <UMErrorCatch/UMErrorCatch.h>

@implementation StatManagerModule {
}

- (instancetype)init
{
    self = [super init];
    if (self) {
    
    }
    
    return self;
}
- (dispatch_queue_t)methodQueue
{
    return dispatch_get_main_queue();
}


HIPPY_EXPORT_MODULE(StatManager)


HIPPY_EXPORT_METHOD(init:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *appKey = params[@"key"];
    NSString *channel = params[@"channel"];
    
    BOOL logEnable = [params[@"logEnable"] boolValue];
    BOOL encrypt = [params[@"encrypt"] boolValue];
    BOOL reportCrash = [params[@"reportCrash"] boolValue];

    [UMCommonLogManager setUpUMCommonLogManager];

    [UMConfigure setLogEnabled:logEnable];
    [UMConfigure setEncryptEnabled:encrypt];
    NSString *deviceID = [UMConfigure deviceIDForIntegration];
    NSLog(@"友盟的deviceID:%@", deviceID);

    [UMConfigure initWithAppkey:appKey channel:channel];

    [MobClick setCrashReportEnabled:reportCrash];

    [UMErrorCatch initErrorCatch];
    resolve(@YES);
}

HIPPY_EXPORT_METHOD(beginPageView:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *name = params[@"name"];

    NSLog(@"beginPageView: %@", name);

    [MobClick beginLogPageView:name];

    resolve(nil);
}

HIPPY_EXPORT_METHOD(endPageView:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *name = params[@"name"];

    NSLog(@"endPageView: %@", name);

    [MobClick endLogPageView:name];
    resolve(nil);
}


HIPPY_EXPORT_METHOD(logPageView:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *name = params[@"name"];
    int seconds = [params[@"seconds"] intValue];

    NSLog(@"logPageView: %@", name);
    NSLog(@"logPageView: %d", seconds);

    [MobClick logPageView:name seconds:seconds];

    resolve(nil);
}

HIPPY_EXPORT_METHOD(event:(nullable NSDictionary *)params resolver:(HippyPromiseResolveBlock)resolve rejecter:(HippyPromiseRejectBlock)reject)
{
    NSString *name = params[@"name"];
    NSString *label = params[@"label"];

    NSLog(@"event name: %@", name);
    NSLog(@"event label: %@", name);

    // TODO add attributes

    [MobClick event:name label:label];

    resolve(nil);
}


@end
