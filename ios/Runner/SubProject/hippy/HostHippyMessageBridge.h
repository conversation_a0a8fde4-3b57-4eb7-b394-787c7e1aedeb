//
//  FlutterHippyMessageBridge.h
//  Runner
//
//  Created by 何飞 on 2020/4/2.
//  Copyright © 2020 The Chromium Authors. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "Singleton.h"
#import "HippyMethodInvoideCallback.h"

NS_ASSUME_NONNULL_BEGIN

@protocol IHostMessageListener <NSObject>
-(void) onHostMessage:(NSString*) messageName messageBody:(NSDictionary*) messageBody;
@end



@interface HostHippyMessageBridge : NSObject
AS_SINGLETON(HostHippyMessageBridge);
-(void) addHostMessageListener: (id<IHostMessageListener>) listener;
-(void) removeHostMessageListener: (id<IHostMessageListener>) listener;
-(void) onHostMessage:(NSString*) messageName messageBody:(NSDictionary*) messageBody;
@end

NS_ASSUME_NONNULL_END
