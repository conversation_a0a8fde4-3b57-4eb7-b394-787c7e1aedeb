//
// Created by mo on 2018/8/23.
//

#import <Foundation/Foundation.h>
#import "Singleton.h"
#import "ABCMacros.h"
#import "WXApi.h"



@interface WXApiHandler : NSObject
AS_SINGLETON(WXApiHandler);
- (void)registerApp:(NSString*) appId enableMTA:(BOOL)enableMTA callback:(void (^)(BOOL success, NSError* error))callback;
- (void)checkWeChatInstallation:(void (^)(BOOL success, NSError* error))callback;

- (void) sendAuthRequestScope:(NSString*) scope state:(NSString*)state openId:(NSString*)openId callback:(void (^)(SendAuthResp* resp, NSError* error))callback;
- (void)shareWithUrl:(NSString*) url title:(NSString*) title desc:(NSString*) desc iconUrl:(NSString*) iconUrl scene:(enum WXScene) sence callback:(void (^)(BOOL success, NSError* error))callback;

- (void)shareWithImage:iconUrl scene:(enum WXScene) sence callback:(void (^)(BOOL success, NSError* error))callback;

- (void)shareWithMiniprogram:(NSString*) title
                        desc:(NSString*) desc
                     iconUrl:(NSString*) iconUrl
                       scene:(enum WXScene) sence
                  webpageUrl:(NSString*) webpageUrl
                    userName:(NSString*) userName
                        path:(NSString*) path
             withShareTicket:(BOOL*) withShareTicket
                    callback:(void (^)(BOOL success, NSError* error))callback;



- (void) paymentWithPartnerId:(NSString*) partnerId prepayId:(NSString*) prepayId nonceStr:(NSString*) nonceStr timeStamp:(NSInteger) timeStamp
         package:(NSString*) package
            sign:(NSString*) sign
        callback:(void (^)(PayResp* rsp, NSError* error))callback;

- (void)launchMiniProgram:(NSString *)userName
                     path:(NSString *)path
          miniProgramType:(WXMiniProgramType)miniProgramType
                 callback:(void (^)(BOOL success, NSError* error))callback;

@end
