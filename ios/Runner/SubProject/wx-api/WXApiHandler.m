//
// Created by mo on 2018/8/23.
//


#import "WXApiHandler.h"
#import "StringUtil.h"
#import "FluwxPlugin.h"
#import "CallResults.h"
#import "WXApi.h"
#import "FluwxKeys.h"
#import "WXApiRequestHandler.h"
#import "FluwxResponseHandler.h"


@interface WXApiHandler()<WXApiManagerDelegate>
@end

typedef void (^OnRecvAuthRsp)(SendAuthResp* rsp);
typedef void (^OnRecvPayRsp)(PayResp* rsp, NSError* error);

@implementation WXApiHandler {
    NSMutableDictionary<NSString*, OnRecvAuthRsp>* _waitRecvAuthRsp;
    OnRecvPayRsp _waitRecvPayRsp;
}

DEF_SINGLETON(WXApiHandler);


static BOOL isWeChatRegistered = NO;
static BOOL handleOpenURLByFluwx = YES;

const NSString* UNIVERSAL_LINK = @"https://app.abcyun.cn/abcyun/";


- (instancetype)init
{
    self = [super init];
    if (self) {
        [FluwxResponseHandler defaultManager].delegate = self;
        
        _waitRecvAuthRsp = [NSMutableDictionary dictionary];
    }
    return self;
}
- (void)registerApp:(NSString*) appId enableMTA:(BOOL)enableMTA callback:(void(^)(BOOL success, NSError* error))callback {
    if (isWeChatRegistered) {
        if (callback)
            callback(YES,nil);
        return;
    }
    
    
    if ([StringUtil isBlank:appId]) {
        if (callback)
            callback(NO, (InvokeError(-1, @"invalid app id are you sure your app id is correct ")));
        return;
    }
    
    isWeChatRegistered = [WXApi registerApp:appId universalLink:UNIVERSAL_LINK];
    if (!isWeChatRegistered) {
        if (callback)
            callback(NO, (InvokeError(-1, ([NSString stringWithFormat:@"App id %@注册失败，请检查", appId]))));
        return;
    }
    
    if (callback)
        callback(YES, nil);
}

- (void)checkWeChatInstallation:(void (^)(BOOL success, NSError* error))callback {
    if (!isWeChatRegistered) {
        if (callback) {
            callback(NO,InvokeError(-1, @"please config  wxapi first"));
        }
        
        return;
    }else{
        if (callback)
            callback([WXApi isWXAppInstalled], nil);
    }
}


- (void) sendAuthRequestScope:(NSString*) scope state:(NSString*)state openId:(NSString*)openId callback:(void (^)(SendAuthResp* resp, NSError* error))callback {
    [WXApiRequestHandler sendAuthRequestScope:scope
                                        State:state
                                       OpenID:(openId == (id) [NSNull null]) ? nil : openId completion:^(BOOL success) {
        if (callback == nil)return;
        if (!success) {
            callback(nil,InvokeError(-1, @"sendAuthRequestScope failed"));
            return;
        }
        
        @synchronized (_waitRecvAuthRsp) {
            [_waitRecvAuthRsp setValue:^(SendAuthResp* authResp){
                dispatch_main_async_safe(^(){
                    callback(authResp, nil);
                });
            } forKey:state];
        }
    }];
    
    
}

- (void)shareWithUrl:(NSString*) url title:(NSString*) title desc:(NSString*) desc iconUrl:(NSString*) iconUrl scene:(enum WXScene) sence callback:(void (^)(BOOL success, NSError* error))callback{
    UIImage* image = nil;
    if (iconUrl.length != 0) {
        image = [UIImage imageWithContentsOfFile:iconUrl];
    }
    
    [WXApiRequestHandler sendLinkURL:url TagName:nil Title:title Description:desc ThumbImage:image MessageExt:nil MessageAction:nil InScene:sence completion:^(BOOL success) {
        callback(success, nil);
    }];
}

- (void)shareWithImage:(NSString*) iconUrl scene:(enum WXScene) sence callback:(void (^)(BOOL success, NSError* error))callback{
    UIImage* image = nil;
    if (iconUrl.length != 0) {
        image = [UIImage imageWithContentsOfFile:iconUrl];
    }
    NSData *imageData = UIImageJPEGRepresentation(image, 0.7);
    [WXApiRequestHandler sendImageData:imageData TagName:nil MessageExt:nil Action:nil ThumbImage:nil InScene:sence title:nil description:nil completion:^(BOOL success) {
        callback(success, nil);
    }];
}

- (void)shareWithMiniprogram:(NSString*) title
                        desc:(NSString*) desc
                     iconUrl:(NSString*) iconUrl
                       scene:(enum WXScene) sence
                  webpageUrl:(NSString*) webpageUrl
                    userName:(NSString*) userName
                        path:(NSString*) path
             withShareTicket:(BOOL*) withShareTicket
                    callback:(void (^)(BOOL success, NSError* error))callback{
    UIImage* image = nil;
    if (iconUrl.length != 0) {
        image = [UIImage imageWithContentsOfFile:iconUrl];
    }
    
    [WXApiRequestHandler sendMiniProgramWebpageUrl:webpageUrl userName:userName path:path title:title Description:desc ThumbImage:image hdImageData:nil withShareTicket:withShareTicket miniProgramType:WXMiniProgramTypeRelease MessageExt:nil MessageAction:nil TagName:nil InScene:sence completion:^(BOOL success) {
        callback(success, nil);
    }];
}


- (void)paymentWithPartnerId:(NSString*) partnerId prepayId:(NSString*) prepayId nonceStr:(NSString*) nonceStr timeStamp:(NSInteger) timeStamp
        package:(NSString*) package
           sign:(NSString*) sign
       callback:(void (^)(PayResp* rsp, NSError* error))callback {
    PayReq* req             = [[PayReq alloc] init];
    req.partnerId           = partnerId;
    req.prepayId            = prepayId;
    req.nonceStr            = nonceStr;
    req.timeStamp           = (UInt32)timeStamp;
    req.package             = package;
    req.sign                = sign;
    
    if (_waitRecvPayRsp) {
        _waitRecvPayRsp(nil, InvokeError(-1, @"取消之前请求"));
    }
    
    _waitRecvPayRsp = nil;
    
    [WXApi sendReq:req completion:^(BOOL success) {
        if (callback == nil)return;
        if (!success) {
            callback(nil,InvokeError(-1, @"payment failed"));
            return;
        }
        
        _waitRecvPayRsp = callback;
    }];
}

- (void)launchMiniProgram:(NSString *)userName
                     path:(NSString *)path
          miniProgramType:(WXMiniProgramType)miniProgramType
                 callback:(void (^)(BOOL success, NSError* error))callback
{
    
    [WXApiRequestHandler launchMiniProgramWithUserName:userName path:path  type:miniProgramType completion:^(BOOL success) {
        callback(success, nil);
    }];
}


#pragma mark --WXApiManagerDelegate--
- (void)managerDidRecvAuthResponse:(SendAuthResp *)response {
    @synchronized (_waitRecvAuthRsp) {
        OnRecvAuthRsp callback = [_waitRecvAuthRsp objectForKey:response.state];
        if (callback !=nil) {
            callback(response);
            [_waitRecvAuthRsp removeObjectForKey:response.state];
        }
    }
}

- (void)managerDidRecvPaymentResponse:(PayResp *)response {
    _waitRecvPayRsp(response, nil);
    _waitRecvPayRsp = nil;
}

@end
