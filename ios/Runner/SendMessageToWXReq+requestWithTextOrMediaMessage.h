//
//  SendMessageToWXReq+requestWithTextOrMediaMessage.h
//  SDKSample
//
//  Created by <PERSON><PERSON><PERSON> on 15/7/14.
//
//

#import "WXApiObject.h"

@interface SendMessageToWXReq (requestWithTextOrMediaMessage)

+ (SendMessageToWXReq *)requestWithText:(NSString *)text
                         OrMediaMessage:(WXMediaMessage *)message
                                  bText:(BOOL)bText
                                InScene:(enum WXScene)scene;
@end
