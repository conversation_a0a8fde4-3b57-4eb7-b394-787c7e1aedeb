//
// Created by mo on 2018/8/15.
//

#import <Foundation/Foundation.h>

extern NSString *const fluwxKeyScene;
extern NSString *const fluwxKeyTimeline;
extern NSString *const fluwxKeySession;
extern NSString *const fluwxKeyFavorite;

extern NSString *const fluwxKeyText;

extern NSString *const fluwxKeyTitle;
extern NSString *const fluwxKeyImage;
extern NSString *const fluwxKeyThumbnail;
extern NSString *const fluwxKeyDescription;

extern NSString *const fluwxKeyPackage;

extern NSString *const fluwxKeyMessageExt;
extern NSString *const fluwxKeyMediaTagName;
extern NSString *const fluwxKeyMessageAction;


extern NSString *const fluwxKeyPlatform;
extern NSString *const fluwxKeyIOS;

extern NSString *const fluwxKeyResult;

@interface FluwxKeys : NSObject
@end
