#include "AppDelegate.h"
#include "FluwxPlugin.h"
#include "WXApi.h"
#include "FluwxResponseHandler.h"
#include "UIUtils.h"
#include "ABCBaseViewController.h"
#include "ABCBaseNavigationController.h"
#import "WXApiHandler.h"
#import "AbcConfig.h"
#import "IQKeyboardManager.h"
#import "Preferences.h"
#import "FileUtils.h"
#import "PluginUtils.h"
#import "HostHippyMessageBridge.h"

#import <CoreText/CoreText.h>
#import <AlipaySDK/AlipaySDK.h>




@interface AppDelegate()

@property (nonatomic, strong) ABCBaseNavigationController *navigationController;

@property (nonatomic, strong) UIView *splashView;


@end
@implementation AppDelegate

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
  self.window = [[UIWindow alloc] initWithFrame:[UIScreen mainScreen].bounds];
  self.hippyViewController = [[HippyViewController alloc] init];
  self.navigationController = [[ABCBaseNavigationController alloc] initWithRootViewController:self.hippyViewController];
  //  [self.navigationController setPresentionStyle:ABCViewControllerPresentionAnimationFlipFromRight];
  [self.navigationController setNavigationBarHidden:YES animated:YES];
  self.window.rootViewController = self.navigationController;
  [self.window makeKeyAndVisible];
  
  //强制使用light模式
  if (@available(iOS 13.0, *)) {
    self.window.overrideUserInterfaceStyle = UIUserInterfaceStyleLight;
  } else {
    // Fallback on earlier versions
  }
  
  [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
  [IQKeyboardManager sharedManager].enableAutoToolbar = NO;
  [IQKeyboardManager sharedManager].enableDebugging = YES;
  [IQKeyboardManager sharedManager].keyboardDistanceFromTextField = 60;
  
  [self loadHippyBundle:YES];
  
  
  UIViewController* controller  = [[UIStoryboard storyboardWithName: @"LaunchScreen" bundle:nil] instantiateInitialViewController];
  self.splashView = controller.view;
  if (self.splashView) {
    // 添加闪屏
    [self.window addSubview: controller.view];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      [self removeSplashView];
    });
  }

  [super application:application didFinishLaunchingWithOptions:launchOptions];
  return YES;
}



-(void) reload {
  [self loadHippyBundle: NO];
}


- (void) removeSplashView {
  if (self.splashView) {
    UIView* view = self.splashView;
    self.splashView = nil;
    [UIView animateWithDuration:0.3 animations:^(void) {
      view.alpha = 0;
    } completion:^(BOOL finished) {
      dispatch_main_async_safe(^{
        [view removeFromSuperview];
      });
    }];
  }
}

- (void) hippyFirstFrameShow {
  [self removeSplashView];
}

- (void) loadHippyBundle: (BOOL) firstLoad{
  NSNumber* debugModeSwitch = [[NSUserDefaults standardUserDefaults] objectForKey:PREFS_HIPPY_DEBUG_SWITCH];
  NSNumber* logLevel = [[NSUserDefaults standardUserDefaults] objectForKey:PREFS_HIPPY_LOG_LEVEL];
  NSNumber* debugMode = debugModeSwitch;
  
  NSString* grayFlag = [[NSUserDefaults standardUserDefaults] objectForKey:PREFS_HIPPY_GRAY_FLAG];
  BOOL isGray = [grayFlag hasPrefix:@"gray"];
  BOOL isPre= [grayFlag hasPrefix:@"pre"];
  NSInteger launchGrayFlag = isGray ? 1: (isPre ? 2 : 0);
  
  NSString* action = @"abcyun://home";
  NSNumber* theme = nil;
  //  NSString* pluginRootDir =[[NSBundle mainBundle] pathForResource:@"plugins/abcyun-hippy" ofType:nil inDirectory:@"res"];;
  NSString* pluginRootDir = [PluginUtils preparePlugin:@"abcyun-hippy" mvIfTmp:firstLoad];
  
  
  
  // Do any additional setup after loading the view.
  BOOL isSimulator = NO;
#if TARGET_IPHONE_SIMULATOR
  isSimulator = YES;
#endif
  
  if (logLevel== nil && (isSimulator || [debugModeSwitch boolValue])) {
    logLevel = @(0);
  }
  
  [self loadHippyFonts:[NSString stringWithFormat:@"%@/assets/fonts", pluginRootDir]];
  
  NSMutableDictionary* params = [[NSMutableDictionary alloc] initWithDictionary: @{@"isSimulator": @(isSimulator), @"userInfo":@"{}",
                                                                                   @"action":action,
                                                                                   @"theme":@([theme intValue]),
                                                                                   @"pwd":pluginRootDir,
                                                                                   @"launchGrayFlag":@(launchGrayFlag)
                                                                                   
  }];
  
  if (logLevel) {
    [params setValue:logLevel forKey:@"logLevel"];
  }
  
  [self.hippyViewController loadBundle:pluginRootDir debug:[debugMode boolValue] params:params];
}

- (BOOL)application:(UIApplication *)app openURL:(NSURL *)url options:(NSDictionary<NSString*, id> *)options
{
  if ([url.host isEqualToString:@"safepay"]) {
    //跳转支付宝钱包进行支付，处理支付结果
    [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:nil];
    return YES;
  }
  
  if ([WXApi handleOpenURL:url delegate:[FluwxResponseHandler defaultManager]])
    return YES;
  
  if([url.scheme isEqualToString:@"www.abcyun.cn"]) {
    [[HostHippyMessageBridge sharedInstance] onHostMessage:@"Push" messageBody:@{
              @"methodName": @"onResumeWebview",
              @"args":@{
                @"url": url.absoluteString
              }
      }];
  }
  
  return [super application:app openURL:url options:options];
}

- (BOOL)application:(UIApplication *)application openURL:(NSURL *)url sourceApplication:(NSString *)sourceApplication annotation:(id)annotation {
  if ([url.host isEqualToString:@"safepay"]) {
    //跳转支付宝钱包进行支付，处理支付结果
    [[AlipaySDK defaultService] processOrderWithPaymentResult:url standbyCallback:nil];
    
    return YES;
  }
  
  if ( [WXApi handleOpenURL:url delegate:[FluwxResponseHandler defaultManager]])
    return YES;
  
  return [super application:application openURL:url sourceApplication:sourceApplication annotation:annotation];
}

- (BOOL)application:(UIApplication *)application continueUserActivity:(NSUserActivity *)userActivity restorationHandler:(void (^)(NSArray<id<UIUserActivityRestoring>> * _Nullable))restorationHandler {
  return [WXApi handleOpenUniversalLink:userActivity delegate:[FluwxResponseHandler defaultManager]];
}

//-(void) initBugly {
//  BuglyConfig * config = [[BuglyConfig alloc] init];
//  [Bugly startWithAppId:@"8b280fd94d" config:config];
//}


-(void) loadHippyFonts:(NSString*) rootDir{
  NSArray* dirs = [[NSFileManager defaultManager] contentsOfDirectoryAtPath:rootDir
                                                                      error:NULL];
  NSMutableArray *ttfFiles = [[NSMutableArray alloc] init];
  [dirs enumerateObjectsUsingBlock:^(id obj, NSUInteger idx, BOOL *stop) {
    NSString *filename = (NSString *)obj;
    NSString *extension = [[filename pathExtension] lowercaseString];
    if ([extension isEqualToString:@"ttf"]) {
      [ttfFiles addObject:[rootDir stringByAppendingPathComponent:filename]];
    }
  }];
  
  for (NSString* fontPath in ttfFiles) {
    CGDataProviderRef fontDataProvider = CGDataProviderCreateWithFilename([fontPath UTF8String]);
    CGFontRef customfont = CGFontCreateWithDataProvider(fontDataProvider);
    CGDataProviderRelease(fontDataProvider);
    CFErrorRef error;
    CTFontManagerRegisterGraphicsFont(customfont, &error);
    if (error){
      // 为了可以重复注册
      CTFontManagerUnregisterGraphicsFont(customfont, &error);
      CTFontManagerRegisterGraphicsFont(customfont, &error);
    }
    CGFontRelease(customfont);
  }
}

@end
