//
// Created by mo on 2018/8/15.
//

#import "FluwxKeys.h"

NSString *const fluwxKeyScene = @"scene";
NSString *const fluwxKeyTimeline = @"timeline";
NSString *const fluwxKeySession = @"session";
NSString *const fluwxKeyFavorite = @"favorite";

NSString *const fluwxKeyText = @"text";

NSString *const fluwxKeyTitle = @"title";
NSString *const fluwxKeyImage = @ "image";
NSString *const fluwxKeyThumbnail = @"thumbnail";
NSString *const fluwxKeyDescription = @"description";

NSString *const fluwxKeyPackage = @"?package=";

NSString *const fluwxKeyMessageExt =@"messageExt";
NSString *const fluwxKeyMediaTagName = @"mediaTagName ";
NSString *const fluwxKeyMessageAction = @"messageAction";

NSString *const fluwxKeyPlatform = @"platform";
NSString *const fluwxKeyIOS=@"iOS";

NSString *const fluwxKeyResult=@"result";

@implementation FluwxKeys {

}
@end