# Uncomment this line to define a global platform for your project
platform :ios, '9.3'
source 'https://github.com/CocoaPods/Specs.git'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def parse_KV_file(file, separator='=')
  file_abs_path = File.expand_path(file)
  if !File.exists? file_abs_path
    return [];
  end
  generated_key_values = {}
  skip_line_start_symbols = ["#", "/"]
  File.foreach(file_abs_path) do |line|
    next if skip_line_start_symbols.any? { |symbol| line =~ /^\s*#{symbol}/ }
    plugin = line.split(pattern=separator)
    if plugin.length == 2
      podname = plugin[0].strip()
      path = plugin[1].strip()
      podpath = File.expand_path("#{path}", file_abs_path)
      generated_key_values[podname] = podpath
    else
      puts "Invalid plugin specification: #{line}"
    end
  end
  generated_key_values
end

target 'Runner' do
  use_frameworks!
end

# Prevent Cocoapods from embedding a second Flutter framework and causing an error with the new Xcode build system.
install! 'cocoapods', :disable_input_output_paths => true

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['SWIFT_VERSION'] = '4.2'
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '9.3'
    end
  end
end


pod 'WechatOpenSDK', '1.8.6'
pod 'AliyunOSSiOS', '2.10.8'
pod 'SDWebImage', '~> 5.0'
pod 'KVOController', '~> 1.2.0'
pod 'Charts', :path => 'third-party/Charts-3.4.0'
pod 'MTBBarcodeScanner', '~> 5.0.11'
pod 'SSZipArchive', '~> 2.2.2'
pod 'Socket.IO-Client-Swift', '~> 15.2.0'

pod 'UMCCommon', '~> 2.1.4'
pod 'UMCCommonLog', '~> 1.0.0'
pod 'UMCAnalytics', '~> 6.1.0'
pod 'UMCErrorCatch', '~> 1.0.0'
pod 'Bugly', '~> 2.5.2'
pod 'AlipaySDK-iOS', '~> 15.6.8'
pod 'CocoaLumberjack', '3.0'
pod 'SVGKit', '~> 3.x'
